@echo off
echo Restarting XAMPP Services for CHealth Wokman...
echo.

echo Stopping Apache...
net stop Apache2.4 2>nul
taskkill /f /im httpd.exe 2>nul

echo Stopping MySQL/MariaDB...
net stop mysql 2>nul
taskkill /f /im mysqld.exe 2>nul

echo Waiting 3 seconds...
timeout /t 3 /nobreak >nul

echo Starting MySQL/MariaDB...
net start mysql
if errorlevel 1 (
    echo Failed to start MySQL service. Trying alternative method...
    cd /d "C:\xampp\mysql\bin"
    start mysqld.exe --console
    timeout /t 5 /nobreak >nul
)

echo Starting Apache...
net start Apache2.4
if errorlevel 1 (
    echo Failed to start Apache service. Trying alternative method...
    cd /d "C:\xampp\apache\bin"
    start httpd.exe
)

echo.
echo Services restarted. Testing database connection...
echo.

cd /d "c:\xampp\htdocs\chealthwokman"
php -r "
try {
    \$pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    echo 'SUCCESS: Database connection working with 127.0.0.1\n';
} catch (Exception \$e) {
    echo 'FAILED: ' . \$e->getMessage() . '\n';
    echo 'Try accessing phpMyAdmin at http://localhost/phpmyadmin\n';
}
"

echo.
echo You can now try accessing: http://localhost/chealthwokman/admin/authenticate
pause
