<?php

namespace App\Models;

use CodeIgniter\Model;

class EducationQualificationsModel extends Model
{
    protected $table = 'education_qualifications';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'qualification_name',
        'qualification_type',
        'description',
        'is_active',
        'sort_order',
        'created_by',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'qualification_name' => 'required|min_length[2]|max_length[255]',
        'qualification_type' => 'required|in_list[basic,additional]',
        'description' => 'permit_empty|max_length[1000]',
        'is_active' => 'permit_empty|in_list[0,1]',
        'sort_order' => 'permit_empty|integer|greater_than_equal_to[0]'
    ];

    protected $validationMessages = [
        'qualification_name' => [
            'required' => 'Qualification name is required.',
            'min_length' => 'Qualification name must be at least 2 characters long.',
            'max_length' => 'Qualification name cannot exceed 255 characters.'
        ],
        'qualification_type' => [
            'required' => 'Qualification type is required.',
            'in_list' => 'Qualification type must be either basic or additional.'
        ]
    ];

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get all active qualifications
     */
    public function getActiveQualifications(): array
    {
        return $this->where('is_active', 1)
                   ->orderBy('qualification_type', 'ASC')
                   ->orderBy('sort_order', 'ASC')
                   ->orderBy('qualification_name', 'ASC')
                   ->findAll();
    }

    /**
     * Get qualifications by type
     */
    public function getQualificationsByType(string $type): array
    {
        return $this->where(['is_active' => 1, 'qualification_type' => $type])
                   ->orderBy('sort_order', 'ASC')
                   ->orderBy('qualification_name', 'ASC')
                   ->findAll();
    }

    /**
     * Get basic qualifications
     */
    public function getBasicQualifications(): array
    {
        return $this->getQualificationsByType('basic');
    }

    /**
     * Get additional qualifications
     */
    public function getAdditionalQualifications(): array
    {
        return $this->getQualificationsByType('additional');
    }

    /**
     * Get qualifications for dropdown
     */
    public function getQualificationsForDropdown(): array
    {
        $qualifications = $this->getActiveQualifications();
        $dropdown = [];
        
        foreach ($qualifications as $qualification) {
            $dropdown[$qualification['id']] = $qualification['qualification_name'];
        }
        
        return $dropdown;
    }

    /**
     * Get qualifications grouped by type for dropdown
     */
    public function getQualificationsGroupedForDropdown(): array
    {
        $basic = $this->getBasicQualifications();
        $additional = $this->getAdditionalQualifications();
        
        $grouped = [];
        
        if (!empty($basic)) {
            $grouped['Basic Qualifications'] = [];
            foreach ($basic as $qualification) {
                $grouped['Basic Qualifications'][$qualification['id']] = $qualification['qualification_name'];
            }
        }
        
        if (!empty($additional)) {
            $grouped['Additional Qualifications'] = [];
            foreach ($additional as $qualification) {
                $grouped['Additional Qualifications'][$qualification['id']] = $qualification['qualification_name'];
            }
        }
        
        return $grouped;
    }
}
