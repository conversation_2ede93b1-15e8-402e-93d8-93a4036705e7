<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<!-- Navigation Buttons -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="<?= base_url('agency/dashboard') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<!-- Page Header -->
<div class="text-center mb-4">
    <h2 class="mb-1">
        <i class="bi bi-building text-primary"></i> Agency Profile
    </h2>
    <p class="text-muted mb-0">Manage your agency information and settings</p>
</div>

<!-- Flash Messages -->
<?php if (session()->getFlashdata('flash_success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        <?= session()->getFlashdata('flash_success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('flash_error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-circle me-2"></i>
        <?= session()->getFlashdata('flash_error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Agency Information Card -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle text-primary"></i> Agency Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= base_url('agency/profile/update') ?>">
                    <?= csrf_field() ?>
                    
                    <!-- Read-only Fields -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2 mb-3">
                                <i class="bi bi-eye"></i> Read-only Information
                            </h6>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="agency_code" class="form-label">Agency Code</label>
                            <input type="text" class="form-control bg-light" id="agency_code" 
                                   value="<?= esc($agency['agency_code']) ?>" readonly>
                            <small class="text-muted">Agency code cannot be modified</small>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">Status</label>
                            <input type="text" class="form-control bg-light" id="status" 
                                   value="<?= ucfirst(esc($agency['status'])) ?>" readonly>
                            <small class="text-muted">Status is managed by system administrators</small>
                        </div>
                    </div>
                    
                    <!-- Editable Fields -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-pencil"></i> Editable Information
                            </h6>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="name" class="form-label">Agency Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?= isset($validation) && $validation->hasError('name') ? 'is-invalid' : '' ?>" 
                                   id="name" name="name" value="<?= old('name', esc($agency['name'])) ?>" 
                                   required maxlength="255">
                            <?php if (isset($validation) && $validation->hasError('name')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('name') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control <?= isset($validation) && $validation->hasError('address') ? 'is-invalid' : '' ?>" 
                                      id="address" name="address" rows="3" maxlength="1000"><?= old('address', esc($agency['address'])) ?></textarea>
                            <?php if (isset($validation) && $validation->hasError('address')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('address') ?>
                                </div>
                            <?php endif; ?>
                            <small class="text-muted">Maximum 1000 characters</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control <?= isset($validation) && $validation->hasError('phone') ? 'is-invalid' : '' ?>" 
                                   id="phone" name="phone" value="<?= old('phone', esc($agency['phone'])) ?>" 
                                   maxlength="20">
                            <?php if (isset($validation) && $validation->hasError('phone')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('phone') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control <?= isset($validation) && $validation->hasError('email') ? 'is-invalid' : '' ?>" 
                                   id="email" name="email" value="<?= old('email', esc($agency['email'])) ?>" 
                                   maxlength="100">
                            <?php if (isset($validation) && $validation->hasError('email')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('email') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <hr class="my-4">
                            <div class="d-flex justify-content-between">
                                <a href="<?= base_url('agency/dashboard') ?>" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Update Agency Profile
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Agency Statistics -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up text-success"></i> Agency Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="p-3 border rounded">
                            <h4 class="text-primary mb-1"><?= esc($agency['agency_code']) ?></h4>
                            <small class="text-muted">Agency Code</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3 border rounded">
                            <h4 class="text-success mb-1"><?= ucfirst(esc($agency['status'])) ?></h4>
                            <small class="text-muted">Current Status</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3 border rounded">
                            <h4 class="text-info mb-1"><?= date('M Y', strtotime($agency['created_at'])) ?></h4>
                            <small class="text-muted">Created</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3 border rounded">
                            <h4 class="text-warning mb-1"><?= date('M d', strtotime($agency['updated_at'])) ?></h4>
                            <small class="text-muted">Last Updated</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
/* Enhanced form styling */
.form-control:focus {
    border-color: #1A4E8C;
    box-shadow: 0 0 0 0.2rem rgba(26, 78, 140, 0.25);
}

.form-control[readonly] {
    background-color: #f8f9fa;
    opacity: 1;
}

/* Card enhancements */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Button styling */
.btn-primary {
    background-color: #1A4E8C;
    border-color: #1A4E8C;
}

.btn-primary:hover {
    background-color: #164080;
    border-color: #164080;
}

/* Statistics cards */
.border {
    border: 1px solid #dee2e6 !important;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.border:hover {
    border-color: #1A4E8C !important;
    box-shadow: 0 2px 4px rgba(26, 78, 140, 0.1);
}

/* Alert styling */
.alert {
    border-radius: 0.5rem;
    border: none;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
    
    .d-flex.justify-content-between .btn {
        width: 100%;
    }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for textarea
    const addressTextarea = document.getElementById('address');
    if (addressTextarea) {
        addressTextarea.addEventListener('input', function() {
            const remaining = 1000 - this.value.length;
            const counter = this.parentNode.querySelector('.text-muted');
            if (counter) {
                counter.textContent = `${remaining} characters remaining`;
                if (remaining < 100) {
                    counter.classList.add('text-warning');
                } else {
                    counter.classList.remove('text-warning');
                }
            }
        });
    }
    
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-danger)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
    
    // Form validation enhancement
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const nameField = document.getElementById('name');
            if (nameField && nameField.value.trim().length < 3) {
                e.preventDefault();
                nameField.focus();
                nameField.classList.add('is-invalid');
                
                // Show or update error message
                let errorDiv = nameField.parentNode.querySelector('.invalid-feedback');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'invalid-feedback';
                    nameField.parentNode.appendChild(errorDiv);
                }
                errorDiv.textContent = 'Agency name must be at least 3 characters long.';
            }
        });
    }
});
</script>
<?= $this->endSection() ?>