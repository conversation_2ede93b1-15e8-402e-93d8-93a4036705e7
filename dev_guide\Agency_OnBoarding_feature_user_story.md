# Agency Portal - Employee Onboarding Feature

## System Overview
The Employee Onboarding Feature serves as the core functionality for registering and managing new employees through a structured workflow within the Agency Portal of the CHS PNG employee management system.

---

## 🎯 Core System Features

### 1. **Employee Registration & Profile Management**
- Create new employee records with basic information
- Multi-step profile completion workflow
- Employee self-service profile access via public links
- Complete employee data management across multiple tables

### 2. **Onboarding Workflow Management**
- Status tracking through multiple approval stages
- Role-based review and approval system
- Automated notifications and status updates
- Comprehensive audit trail for all onboarding activities

### 3. **Document & Information Collection**
- Structured data collection based on CHS Bio-Data forms
- Educational qualification tracking
- Professional membership and licensing information
- Employment history documentation
- Banking and NASFUND details management

### 4. **Public Profile Access**
- Secure public link generation for employee self-service
- Controlled access during pending onboarding status
- Employee document upload and profile completion
- Agency control over public access availability

### 5. **Multi-Level Approval System**
- Onboarding status management
- HR review and approval
- Supervisor review and approval
- CEO/Executive approval workflow
- Final employment status determination

---

## 📋 Database Schema Requirements

### Employee Tables Structure

#### employee_personal_information Table
```sql
CREATE TABLE employee_personal_information (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employment_number VARCHAR(20) UNIQUE NOT NULL,
    agency_id INT NOT NULL,
    nid_number VARCHAR(20) UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50),
    last_name VARCHAR(50) NOT NULL,
    email_address VARCHAR(100) UNIQUE NOT NULL,
    gender ENUM('male', 'female') NOT NULL,
    date_of_birth DATE,
    date_of_commencement DATE,
    marital_status ENUM('single', 'married', 'divorced', 'widowed'),
    spouse_name VARCHAR(100),
    number_of_children INT DEFAULT 0,
    home_province VARCHAR(50),
    home_district VARCHAR(50),
    home_village VARCHAR(100),
    mobile_number VARCHAR(20),
    emergency_contact_person VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    designation VARCHAR(100),
    department VARCHAR(50),
    
    -- Banking Details
    bank_name VARCHAR(100),
    bank_branch VARCHAR(100),
    account_name VARCHAR(255),
    account_number VARCHAR(50),
    account_type VARCHAR(50),
    
    -- NASFUND Details
    nasfund_member_name VARCHAR(255),
    nasfund_membership_number VARCHAR(50),
    nasfund_year_joined YEAR,
    nasfund_branch VARCHAR(100),
    
    -- Process Fields
    onboard_status ENUM('pending', 'in_progress', 'completed', 'rejected') DEFAULT 'pending',
    onboard_status_at DATETIME,
    onboard_status_by INT,
    onboard_status_remarks TEXT,
    
    hr_review_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    hr_review_status_at DATETIME,
    hr_review_status_by INT,
    
    supervisor_review_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    supervisor_review_status_at DATETIME,
    supervisor_review_status_by INT,
    
    ceo_review_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    ceo_review_status_at DATETIME,
    ceo_review_status_by INT,
    
    employment_status ENUM('pending', 'active', 'inactive', 'terminated') DEFAULT 'pending',
    employment_status_at DATETIME,
    employment_status_by INT,
    
    public_profile_enabled TINYINT(1) DEFAULT 1,
    public_profile_token VARCHAR(255) UNIQUE,
    public_profile_expires_at DATETIME,
    
    is_active TINYINT(1) DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (agency_id) REFERENCES agencies(id),
    INDEX idx_employment_number (employment_number),
    INDEX idx_agency_id (agency_id),
    INDEX idx_onboard_status (onboard_status),
    INDEX idx_employment_status (employment_status)
);
```

#### employee_education Table
```sql
CREATE TABLE employee_education (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    qualification_type ENUM('basic', 'additional') NOT NULL,
    qualification_name VARCHAR(255) NOT NULL,
    institution VARCHAR(255),
    course_duration VARCHAR(50),
    completion_year YEAR,
    certificate_number VARCHAR(100),
    is_verified TINYINT(1) DEFAULT 0,
    document_path VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employee_personal_information(id) ON DELETE CASCADE,
    INDEX idx_employee_qualification (employee_id, qualification_type)
);
```

#### employee_professional_membership Table
```sql
CREATE TABLE employee_professional_membership (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    professional_affiliation VARCHAR(255),
    license_number VARCHAR(100),
    current_status ENUM('active', 'expired', 'suspended') DEFAULT 'active',
    renewal_date DATE,
    document_path VARCHAR(500),
    is_verified TINYINT(1) DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employee_personal_information(id) ON DELETE CASCADE,
    INDEX idx_employee_membership (employee_id)
);
```

#### employee_employment_history Table
```sql
CREATE TABLE employee_employment_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    employer_name VARCHAR(255) NOT NULL,
    designation VARCHAR(100),
    employment_duration VARCHAR(50),
    start_date DATE,
    end_date DATE,
    is_current TINYINT(1) DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employee_personal_information(id) ON DELETE CASCADE,
    INDEX idx_employee_history (employee_id)
);
```

---

## 👤 User Stories

### **Employee Registration & Basic Setup**

#### Story #1: Create New Employee Record
**As an** Agency Portal user  
**I want to** create a new employee record with basic information  
**So that** I can initiate the onboarding process for new hires  

**Acceptance Criteria:**
- Access onboarding feature from agency dashboard
- Enter basic employee information (Full Name, Email Address)
- employment number can be empty, will be entered later in the process
- Employee record linked to current user's agency_id
- Click "Create" button to save initial record
- Redirect to employee profile page for complete information entry
- Display success message with employee ID
- Set initial onboard_status to 'pending'

#### Story #2: Complete Employee Profile Information
**As an** Agency Portal user  
**I want to** complete all employee profile information  
**So that** comprehensive employee records are maintained  

**Acceptance Criteria:**
- Access employee profile page after initial creation
- Complete personal information section (NID, contact details, family info)
- Enter employment details (designation, department, commencement date)
- Add banking information (bank details, account information)
- Input NASFUND member details
- Save profile information with validation
- Display progress indicator showing completion status
- Show required field validation messages

---

### **Educational Qualifications Management**

#### Story #3: Add Employee Educational Qualifications
**As an** Agency Portal user  
**I want to** record employee educational qualifications  
**So that** academic credentials are properly documented  

**Acceptance Criteria:**
- Add basic qualification (required) with institution and completion details
- Add multiple additional qualifications as needed
- Specify qualification type (basic/additional)
- Enter institution name, course duration, completion year
- Option to add certificate numbers
- Upload supporting documents for qualifications
- Mark qualifications as verified/unverified
- Display all qualifications in organized list format

#### Story #4: Manage Professional Memberships
**As an** Agency Portal user  
**I want to** track employee professional memberships and licenses  
**So that** professional credentials and compliance are maintained  

**Acceptance Criteria:**
- Add professional affiliation details
- Record license numbers and renewal dates
- Track license status (active, expired, suspended)
- Upload professional membership documents
- Set renewal date reminders
- Verify professional credentials
- Display membership history and current status

---

### **Employment History Tracking**

#### Story #5: Record Employment History
**As an** Agency Portal user  
**I want to** document employee previous employment history  
**So that** work experience is properly recorded  

**Acceptance Criteria:**
- Add multiple previous employment records
- Enter employer name, designation, and duration
- Specify employment start and end dates
- Calculate employment duration automatically
- Mark current employment if applicable
- Display employment history in chronological order
- Validate date ranges for logical consistency

---

### **Public Profile Access & Employee Self-Service**

#### Story #6: Generate Public Profile Access
**As an** Agency Portal user  
**I want to** provide employees with self-service access to their profiles  
**So that** employees can complete their own information and upload documents  

**Acceptance Criteria:**
- Generate secure public profile link for employee
- "Send Employee Public Profile" button next to email field
- Send email invitation with secure access link to employee
- Set link expiration date (default 30 days)
- Employee can access profile only when onboard_status is 'pending'
- Display clear instructions for employee profile completion
- Public link includes unique token for security

#### Story #7: Control Public Profile Access
**As an** Agency Portal user  
**I want to** enable or disable employee public profile access  
**So that** I can control when employees can modify their information  

**Acceptance Criteria:**
- Toggle switch to enable/disable public profile access
- Visual indicator showing current public access status
- Disable access automatically when onboarding status changes from 'pending'
- Option to extend or regenerate access links
- Notify employee when access is disabled or expires
- Maintain audit log of access control changes

---

### **Onboarding Workflow Management**

#### Story #8: Manage Onboarding Status
**As an** Agency Portal user  
**I want to** track and update employee onboarding status  
**So that** I can monitor progress through the onboarding process  

**Acceptance Criteria:**
- View current onboarding status (pending, in_progress, completed, rejected)
- Update onboarding status with remarks/comments
- Timestamp and record user making status changes
- Display onboarding progress indicators
- Send automatic notifications on status changes
- Prevent status regression without proper authorization
- Show onboarding timeline and milestones

#### Story #9: Submit for Review Approvals
**As an** Agency Portal user  
**I want to** submit completed employee profiles for approval  
**So that** proper authorization workflow is followed  

**Acceptance Criteria:**
- Validate profile completeness before submission
- Submit for HR review when profile is complete
- Track HR review status (pending, approved, rejected)
- Forward to supervisor review after HR approval
- Submit to CEO/Executive for final approval
- Display current approval stage and responsible reviewer
- Send notifications to appropriate reviewers
- Allow reviewers to add comments and feedback

#### Story #10: Finalize Employment Status
**As an** Agency Portal user  
**I want to** set final employment status after all approvals  
**So that** employee onboarding is properly completed  

**Acceptance Criteria:**
- Update employment status after all approvals are complete
- Choose from employment status options (pending, active, inactive, terminated)
- Record employment start date and responsible person
- Generate employment confirmation documents
- Send welcome notification to new employee
- Update employee access permissions based on status
- Archive onboarding documents and workflow history

---

### **Employee Self-Service (Public Profile)**

#### Story #11: Employee Profile Self-Completion
**As an** Employee accessing my public profile  
**I want to** complete my profile information online  
**So that** I can provide accurate personal details and upload required documents  

**Acceptance Criteria:**
- Access profile via secure public link from email invitation
- View profile completion progress and requirements
- Complete personal information sections
- Upload required documents (certificates, ID, medical clearance, etc.)
- Save progress and return later using same link
- Submit completed profile for review
- Receive confirmation of successful submission
- Access restricted to when onboard_status is 'pending'

#### Story #12: Employee Document Upload
**As an** Employee accessing my public profile  
**I want to** upload required documents  
**So that** my onboarding requirements are fulfilled  

**Acceptance Criteria:**
- View document requirements checklist
- Upload multiple document types (PDF, images)
- Preview uploaded documents before submission
- Replace or update previously uploaded documents
- Track document upload status and verification
- Receive feedback on document approval/rejection
- Download document requirement templates if available

---

### **Onboarding Dashboard & Reporting**

#### Story #13: Onboarding Dashboard Overview
**As an** Agency Portal user  
**I want to** view onboarding status overview  
**So that** I can monitor all employee onboarding activities  

**Acceptance Criteria:**
- Display total employees by onboarding status
- Show pending approvals requiring action
- List recent onboarding activities
- Display employees with expiring public profile access
- Show completion rates and average onboarding time
- Quick action buttons for common onboarding tasks
- Filter employees by onboarding stage

#### Story #14: Generate Onboarding Reports
**As an** Agency Portal user  
**I want to** generate onboarding progress reports  
**So that** I can track performance and identify bottlenecks  

**Acceptance Criteria:**
- Generate onboarding status reports by date range
- Export employee lists by onboarding stage
- Create approval workflow reports
- Show onboarding completion statistics
- Generate document compliance reports
- Export data in PDF and Excel formats
- Schedule automated reports via email

---

## 🎨 UI/UX Requirements

### Onboarding Interface Design

#### Employee Creation Workflow
```html
<!-- Step 1: Basic Information -->
<div class="card">
    <div class="card-header">
        <h5><i class="bi bi-person-plus"></i> Create New Employee</h5>
    </div>
    <div class="card-body">
        <form>
            <div class="row">
                <div class="col-md-4">
                    <label>First Name *</label>
                    <input type="text" class="form-control" required>
                </div>
                <div class="col-md-4">
                    <label>Middle Name</label>
                    <input type="text" class="form-control">
                </div>
                <div class="col-md-4">
                    <label>Last Name *</label>
                    <input type="text" class="form-control" required>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-8">
                    <label>Email Address *</label>
                    <input type="email" class="form-control" required>
                </div>
                <div class="col-md-4">
                    <label>&nbsp;</label>
                    <button type="button" class="btn btn-outline-primary form-control">
                        <i class="bi bi-envelope"></i> Send Public Profile
                    </button>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="publicAccess" checked>
                        <label class="form-check-label" for="publicAccess">
                            Enable employee public profile access
                        </label>
                    </div>
                </div>
            </div>
            <div class="text-end mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check"></i> Create Employee
                </button>
            </div>
        </form>
    </div>
</div>
```

#### Employee Profile Completion Interface
```html
<!-- Profile Completion with Progress Indicator -->
<div class="row">
    <div class="col-md-3">
        <!-- Progress Sidebar -->
        <div class="card">
            <div class="card-header">
                <h6>Profile Completion</h6>
                <div class="progress">
                    <div class="progress-bar" style="width: 65%">65%</div>
                </div>
            </div>
            <div class="list-group list-group-flush">
                <a href="#personal" class="list-group-item active">
                    <i class="bi bi-person"></i> Personal Info
                </a>
                <a href="#education" class="list-group-item">
                    <i class="bi bi-mortarboard"></i> Education
                </a>
                <a href="#professional" class="list-group-item">
                    <i class="bi bi-award"></i> Professional
                </a>
                <a href="#employment" class="list-group-item">
                    <i class="bi bi-briefcase"></i> Employment History
                </a>
                <a href="#banking" class="list-group-item">
                    <i class="bi bi-bank"></i> Banking Details
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <!-- Main Profile Content -->
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <h5>Employee Profile</h5>
                <div>
                    <span class="badge bg-warning">Pending Onboarding</span>
                    <button class="btn btn-sm btn-outline-success">Submit for Review</button>
                </div>
            </div>
            <!-- Profile sections content -->
        </div>
    </div>
</div>
```

### Onboarding Status Workflow
```html
<!-- Status Workflow Display -->
<div class="card mt-3">
    <div class="card-header">
        <h6>Onboarding Workflow Status</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 text-center">
                <div class="workflow-step completed">
                    <i class="bi bi-check-circle-fill text-success"></i>
                    <h6>Profile Creation</h6>
                    <small>Completed</small>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="workflow-step active">
                    <i class="bi bi-hourglass-split text-warning"></i>
                    <h6>HR Review</h6>
                    <small>Pending</small>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="workflow-step">
                    <i class="bi bi-circle text-muted"></i>
                    <h6>Supervisor Review</h6>
                    <small>Not Started</small>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="workflow-step">
                    <i class="bi bi-circle text-muted"></i>
                    <h6>Final Approval</h6>
                    <small>Not Started</small>
                </div>
            </div>
        </div>
    </div>
</div>
```

---

## 🔐 Security Requirements

### Public Profile Access Security
- **Secure Token Generation**: UUID-based tokens for public profile access
- **Time-Limited Access**: Configurable expiration dates for public links
- **Status-Based Access Control**: Access only when onboard_status is 'pending'
- **Session Management**: Secure session handling for public profile access
- **Data Validation**: Strict input validation for all employee data

### Onboarding Workflow Security
- **Role-Based Approvals**: Appropriate permissions for each approval level
- **Audit Trail**: Complete tracking of all status changes and approvals
- **Data Encryption**: Sensitive information encrypted at rest
- **Access Logging**: Log all access to employee onboarding data
- **Document Security**: Secure file upload and storage for employee documents

---

## 🔧 Technical Implementation

### CodeIgniter 4 Controller Structure
```php
// app/Controllers/Agency/OnboardingController.php
class OnboardingController extends AgencyBaseController
{
    public function index()
    {
        // Display onboarding dashboard
    }
    
    public function create()
    {
        // Show employee creation form
    }
    
    public function store()
    {
        // Save new employee basic information
        // Generate employment number
        // Create public profile token
        // Send email invitation if enabled
    }
    
    public function profile($employee_id)
    {
        // Show employee profile completion page
    }
    
    public function updateProfile($employee_id)
    {
        // Update employee profile information
    }
    
    public function generatePublicLink($employee_id)
    {
        // Generate new public profile access link
    }
    
    public function togglePublicAccess($employee_id)
    {
        // Enable/disable public profile access
    }
    
    public function submitForReview($employee_id)
    {
        // Submit employee for approval workflow
    }
    
    public function updateOnboardingStatus($employee_id)
    {
        // Update onboarding status with workflow management
    }
}
```

### Public Profile Controller
```php
// app/Controllers/PublicProfileController.php
class PublicProfileController extends BaseController
{
    public function access($token)
    {
        // Validate token and provide employee access
        // Check token expiration and onboard_status
    }
    
    public function update($token)
    {
        // Allow employee to update their profile
    }
    
    public function uploadDocument($token)
    {
        // Handle document uploads from employee
    }
    
    public function submit($token)
    {
        // Submit completed profile back to agency
    }
}
```

---

## 📈 Implementation Roadmap

### Phase 1: Core Employee Registration (Week 1-2)
1. **Database Schema Implementation**
   - Create all employee-related tables with proper relationships
   - Implement data migrations and seeders
   - Set up proper indexing for performance

2. **Basic Employee Creation**
   - Implement employee creation form with validation
   - Generate unique employment numbers
   - Create employee profile pages with multi-section layout

### Phase 2: Profile Management & Public Access (Week 3-4)
1. **Profile Completion Interface**
   - Build comprehensive profile forms for all sections
   - Implement progress tracking and validation
   - Create document upload functionality

2. **Public Profile System**
   - Develop secure token-based public access
   - Create employee self-service interface
   - Implement email invitation system

### Phase 3: Workflow & Approval System (Week 5-6)
1. **Onboarding Workflow**
   - Implement multi-level approval system
   - Create status tracking and management
   - Build notification system for workflow updates

2. **Dashboard & Reporting**
   - Develop onboarding dashboard with metrics
   - Create progress tracking and reporting tools
   - Implement search and filtering capabilities

### Phase 4: Integration & Enhancement (Week 7-8)
1. **System Integration**
   - Connect with document management system
   - Integrate with email notification service
   - Implement audit logging and security features

2. **Testing & Optimization**
   - Comprehensive testing of all workflows
   - Performance optimization and security testing
   - User acceptance testing and bug fixes