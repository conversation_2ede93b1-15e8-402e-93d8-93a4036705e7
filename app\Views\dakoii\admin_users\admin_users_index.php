<!-- <PERSON>er -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-light">Admin Users</h1>
        <p class="text-muted mb-0">Manage organization admin users and their permissions</p>
    </div>
    <div>
        <a href="<?= base_url('dakoii/admin-users/create') ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            Create New Admin User
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= base_url('dakoii/admin-users') ?>" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search</label>
                <input type="text" 
                       class="form-control" 
                       id="search" 
                       name="search" 
                       value="<?= esc($search) ?>" 
                       placeholder="Search by name, username, or email...">
            </div>
            <div class="col-md-6 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-search me-1"></i>
                    Search
                </button>
                <a href="<?= base_url('dakoii/admin-users') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Admin Users Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="bi bi-people me-2"></i>
            Admin Users List
        </h5>
        <span class="badge bg-primary">
            Total: <?= count($adminUsers) ?> users
        </span>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($adminUsers)): ?>
        <div class="table-responsive">
            <table class="table table-dark table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th>Name</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th width="150">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($adminUsers as $user): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                    <i class="bi bi-person-fill text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></div>
                                    <?php if (!empty($user['phone_number'])): ?>
                                    <small class="text-muted"><?= esc($user['phone_number']) ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="font-monospace"><?= esc($user['username']) ?></span>
                        </td>
                        <td>
                            <a href="mailto:<?= esc($user['email']) ?>" class="text-decoration-none">
                                <?= esc($user['email']) ?>
                            </a>
                        </td>
                        <td>
                            <?php
                            $roleClass = 'bg-secondary';
                            if ($user['role'] === 'admin') $roleClass = 'bg-danger';
                            elseif ($user['role'] === 'ceo') $roleClass = 'bg-dark';
                            elseif ($user['role'] === 'manager') $roleClass = 'bg-warning';
                            elseif ($user['role'] === 'user') $roleClass = 'bg-info';
                            ?>
                            <span class="badge <?= $roleClass ?>">
                                <?= ucfirst(esc($user['role'])) ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($user['is_active']): ?>
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Active
                                </span>
                            <?php else: ?>
                                <span class="badge bg-secondary">
                                    <i class="bi bi-x-circle me-1"></i>
                                    Inactive
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?= date('M j, Y', strtotime($user['created_at'])) ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="<?= base_url('dakoii/admin-users/' . $user['id']) ?>" 
                                   class="btn btn-outline-info" 
                                   title="View">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="<?= base_url('dakoii/admin-users/' . $user['id'] . '/edit') ?>" 
                                   class="btn btn-outline-warning" 
                                   title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form method="POST" 
                                      action="<?= base_url('dakoii/admin-users/' . $user['id'] . '/delete') ?>" 
                                      class="d-inline"
                                      onsubmit="return confirm('Are you sure you want to delete this admin user?')">
                                    <?= csrf_field() ?>
                                    <button type="submit" 
                                            class="btn btn-outline-danger" 
                                            title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($pager->getPageCount() > 1): ?>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing <?= $pager->getFirstPage() ?> to <?= $pager->getLastPage() ?> of <?= $pager->getTotal() ?> results
                </div>
                <div>
                    <?= $pager->links() ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h4 class="mt-3 text-muted">No Admin Users Found</h4>
            <?php if (!empty($search)): ?>
                <p class="text-muted">No admin users match your search criteria.</p>
                <a href="<?= base_url('dakoii/admin-users') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i>
                    View All Users
                </a>
            <?php else: ?>
                <p class="text-muted">Get started by creating your first admin user.</p>
                <a href="<?= base_url('dakoii/admin-users/create') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    Create First Admin User
                </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>
