<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DropEmployeeTables extends Migration
{
    public function up()
    {
        // Drop employee-related tables in correct order (considering foreign keys)
        
        // Drop tables that reference employees first
        if ($this->db->tableExists('employee_profile_links')) {
            $this->forge->dropTable('employee_profile_links');
        }
        
        if ($this->db->tableExists('employee_qualifications')) {
            $this->forge->dropTable('employee_qualifications');
        }
        
        if ($this->db->tableExists('employee_documents')) {
            $this->forge->dropTable('employee_documents');
        }
        
        if ($this->db->tableExists('employee_banking')) {
            $this->forge->dropTable('employee_banking');
        }
        
        // Drop the main employees table last
        if ($this->db->tableExists('employees')) {
            $this->forge->dropTable('employees');
        }
    }

    public function down()
    {
        // This migration is irreversible - employee management has been removed
        // If you need to restore employee functionality, you would need to:
        // 1. Restore the original migration files
        // 2. Restore the model files
        // 3. Restore the controller files
        // 4. Restore the view files
        // 5. Run the migrations again
        
        throw new \Exception('Cannot reverse this migration - employee management has been permanently removed');
    }
}
