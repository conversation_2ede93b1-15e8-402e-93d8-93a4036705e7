<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<style>
/* Mobile-friendly action buttons */
@media (max-width: 767.98px) {
    .action-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.25rem;
        min-width: 45px;
    }
    .action-btn i {
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
    }
    .action-btn span {
        font-size: 0.65rem;
        line-height: 1;
    }
}

@media (min-width: 768px) {
    .action-btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.5rem;
    }
    .action-btn i {
        font-size: 1rem;
    }
    .action-btn span {
        font-size: 0.75rem;
    }
}
</style>

<!-- Navigation Buttons -->
<div class="row mb-4">
    <div class="col-6">
        <a href="<?= base_url('agency/onboarding') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Employee List
        </a>
    </div>
    <div class="col-6 text-end">
        <!-- Additional actions can be added here -->
    </div>
</div>

<!-- Onboarding Steps Navigation -->
<div class="row mb-4">
    <div class="col-md-3 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/profile') ?>" class="btn btn-outline-primary w-100">
            <i class="bi bi-person"></i><br>
            <small>Personal Info</small>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/education') ?>" class="btn btn-outline-info w-100">
            <i class="bi bi-mortarboard"></i><br>
            <small>Education</small>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <button class="btn btn-success w-100" disabled>
            <i class="bi bi-award"></i><br>
            <small>Memberships</small>
        </button>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/files') ?>" class="btn btn-outline-warning w-100">
            <i class="bi bi-file-earmark"></i><br>
            <small>Files</small>
        </a>
    </div>
    <div class="col-md-3 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/employment-history') ?>" class="btn btn-outline-warning w-100">
            <i class="bi bi-briefcase"></i><br>
            <small>Employment</small>
        </a>
    </div>
</div>

<!-- Page Header -->
<div class="text-center mb-4">
    <h2 class="mb-1">
        <i class="bi bi-person-circle text-primary"></i>
        <?= esc($employee['first_name'] . ' ' . $employee['last_name']) ?>
    </h2>
    <p class="text-muted mb-0">
        <?php if (!empty($employee['employment_number'])): ?>
            File Number: <?= esc($employee['employment_number']) ?> •
        <?php endif; ?>
        Professional Memberships & Licenses
    </p>
</div>

<!-- Membership Stats Row -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success"><?= $stats['active_memberships'] ?></h4>
                <p class="text-muted mb-0">Active</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-danger"><?= $stats['expired_memberships'] ?></h4>
                <p class="text-muted mb-0">Expired</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning"><?= $stats['expiring_soon'] ?></h4>
                <p class="text-muted mb-0">Expiring Soon</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info"><?= count($membership_records) ?></h4>
                <p class="text-muted mb-0">Total Records</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Memberships Content -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-award text-success"></i>
            Professional Memberships & Licenses
        </h5>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMembershipModal">
                <i class="bi bi-plus-circle"></i> Add Membership
            </button>
        </div>
            </div>
            <div class="card-body">
                <?php if (empty($membership_records)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-award text-muted" style="font-size: 4rem;"></i>
                        <h5 class="text-muted mt-3">No professional memberships found</h5>
                        <p class="text-muted">Start by adding the employee's professional memberships and licenses.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMembershipModal">
                            <i class="bi bi-plus-circle"></i> Add First Membership
                        </button>
                    </div>
                <?php else: ?>
                    <!-- Membership Records Table -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" width="25%">
                                        <i class="bi bi-award text-primary"></i> Professional Affiliation
                                    </th>
                                    <th scope="col" width="15%">
                                        <i class="bi bi-card-text text-info"></i> License Number
                                    </th>
                                    <th scope="col" width="12%">
                                        <i class="bi bi-check-circle text-success"></i> Status
                                    </th>
                                    <th scope="col" width="12%">
                                        <i class="bi bi-calendar-event text-warning"></i> Renewal Date
                                    </th>
                                    <th scope="col" width="8%">
                                        <i class="bi bi-file-earmark text-info"></i> Document
                                    </th>
                                    <th scope="col" width="12%">
                                        <i class="bi bi-clock text-muted"></i> Added
                                    </th>
                                    <th scope="col" width="12%">
                                        <i class="bi bi-gear text-muted"></i> Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($membership_records as $membership): ?>
                                    <?php
                                    $statusColors = [
                                        'active' => 'success',
                                        'expired' => 'danger',
                                        'suspended' => 'warning',
                                        'pending' => 'info'
                                    ];
                                    $statusColor = $statusColors[$membership['current_status']] ?? 'secondary';
                                    
                                    // Check if expiring soon (within 30 days)
                                    $isExpiringSoon = false;
                                    if (!empty($membership['renewal_date'])) {
                                        $renewalDate = new DateTime($membership['renewal_date']);
                                        $today = new DateTime();
                                        $diff = $today->diff($renewalDate);
                                        $isExpiringSoon = $diff->days <= 30 && $renewalDate > $today;
                                    }
                                    ?>
                                    <tr>
                                        <td>
                                            <div class="fw-semibold text-dark">
                                                <?= esc($membership['professional_affiliation']) ?>
                                            </div>
                                            <?php if ($isExpiringSoon): ?>
                                                <small class="text-warning">
                                                    <i class="bi bi-exclamation-triangle"></i> Expiring Soon
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="text-dark">
                                                <?= !empty($membership['license_number']) ? esc($membership['license_number']) : '<span class="text-muted">-</span>' ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-dark">
                                                <?= ucfirst($membership['current_status']) ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <?php if (!empty($membership['renewal_date'])): ?>
                                                    <span class="text-dark">
                                                        <?= date('M d, Y', strtotime($membership['renewal_date'])) ?>
                                                    </span>
                                                    <?php if ($isExpiringSoon): ?>
                                                        <br><small class="text-warning">⚠️ Soon</small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <?php if (!empty($membership['document_path'])): ?>
                                                    <a href="<?= base_url($membership['document_path']) ?>" target="_blank"
                                                       class="btn btn-sm btn-outline-success" title="View Document">
                                                        <i class="bi bi-file-earmark-check"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">
                                                        <i class="bi bi-file-earmark-x"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?= date('M d, Y', strtotime($membership['created_at'])) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1 justify-content-center">
                                                <button class="btn btn-sm btn-outline-primary action-btn" type="button"
                                                        onclick="editMembership(<?= $membership['id'] ?>)"
                                                        title="Edit Membership">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <?php if (!empty($membership['document_path'])): ?>
                                                    <a href="<?= base_url($membership['document_path']) ?>" target="_blank"
                                                       class="btn btn-sm btn-outline-info action-btn" title="View Document">
                                                        <i class="bi bi-download"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <button class="btn btn-sm btn-outline-danger action-btn" type="button"
                                                        onclick="deleteMembership(<?= $membership['id'] ?>)"
                                                        title="Delete Membership">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Table Summary -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i>
                                Showing <?= count($membership_records) ?> membership record<?= count($membership_records) !== 1 ? 's' : '' ?>
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                <span class="fw-medium text-success"><?= $stats['active_memberships'] ?></span> Active,
                                <span class="fw-medium text-danger"><?= $stats['expired_memberships'] ?></span> Expired,
                                <span class="fw-medium text-warning"><?= $stats['expiring_soon'] ?></span> Expiring Soon
                            </small>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Membership Modal -->
<div class="modal fade" id="addMembershipModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Professional Membership</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addMembershipForm" method="POST" action="<?= base_url('agency/onboarding/' . $employee['id'] . '/memberships/store') ?>" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="professional_affiliation" class="form-label">Professional Affiliation <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="professional_affiliation" name="professional_affiliation" 
                                       placeholder="e.g., Papua New Guinea Institute of Engineers" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="license_number" class="form-label">License/Member Number</label>
                                <input type="text" class="form-control" id="license_number" name="license_number">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="current_status" class="form-label">Current Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="current_status" name="current_status" required>
                                    <option value="">Select Status</option>
                                    <option value="active">Active</option>
                                    <option value="expired">Expired</option>
                                    <option value="suspended">Suspended</option>
                                    <option value="pending">Pending</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="renewal_date" class="form-label">Renewal Date</label>
                                <input type="date" class="form-control" id="renewal_date" name="renewal_date">
                                <div class="form-text">When does this membership/license expire?</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="document" class="form-label">Certificate/License Document</label>
                        <input type="file" class="form-control" id="document" name="document" 
                               accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                        <div class="form-text">Accepted formats: PDF, JPG, PNG, DOC, DOCX (Max: 5MB)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span class="btn-text">Save Membership</span>
                        <span class="btn-loading d-none">
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Saving...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Membership Modal -->
<div class="modal fade" id="editMembershipModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Professional Membership</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editMembershipForm" method="POST" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <!-- Form fields will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span class="btn-text">Update Membership</span>
                        <span class="btn-loading d-none">
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Updating...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteMembershipModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this professional membership record? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteMembershipForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">
                        <span class="btn-text">Delete</span>
                        <span class="btn-loading d-none">
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Deleting...
                        </span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Membership records data for JavaScript
const membershipRecords = <?= json_encode($membership_records) ?>;
const baseUrl = '<?= base_url() ?>';
const employeeId = <?= $employee['id'] ?>;

// AJAX Form Handler
class MembershipManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // Add membership form
        document.getElementById('addMembershipForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit(e.target, 'add');
        });

        // Edit membership form
        document.getElementById('editMembershipForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit(e.target, 'edit');
        });

        // Delete membership form
        document.getElementById('deleteMembershipForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleDelete(e.target);
        });
    }

    async handleFormSubmit(form, type) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');
        
        // Show loading state
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        submitBtn.disabled = true;

        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('success', result.message);
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(form.closest('.modal'));
                if (modal) modal.hide();
                
                // Reset form
                form.reset();
                
                // Reload page to show updated data
                setTimeout(() => {
                    // Show loading overlay
                    const loadingOverlay = document.createElement('div');
                    loadingOverlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
                    loadingOverlay.style.backgroundColor = 'rgba(255,255,255,0.8)';
                    loadingOverlay.style.zIndex = '9999';
                    loadingOverlay.innerHTML = `
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="mt-2">Updating membership list...</div>
                        </div>
                    `;
                    document.body.appendChild(loadingOverlay);
                    
                    window.location.reload();
                }, 1000);
            } else {
                this.showAlert('error', result.message);
                if (result.errors) {
                    this.displayValidationErrors(form, result.errors);
                }
            }
        } catch (error) {
            console.error('Error:', error);
            this.showAlert('error', 'An unexpected error occurred. Please try again.');
        } finally {
            // Reset button state
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
            submitBtn.disabled = false;
        }
    }

    async handleDelete(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');
        
        // Show loading state
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        submitBtn.disabled = true;

        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('success', result.message);
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(form.closest('.modal'));
                if (modal) modal.hide();
                
                // Reload page to show updated data
                setTimeout(() => {
                    // Show loading overlay for delete
                    const loadingOverlay = document.createElement('div');
                    loadingOverlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
                    loadingOverlay.style.backgroundColor = 'rgba(255,255,255,0.8)';
                    loadingOverlay.style.zIndex = '9999';
                    loadingOverlay.innerHTML = `
                        <div class="text-center">
                            <div class="spinner-border text-danger" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="mt-2">Removing membership...</div>
                        </div>
                    `;
                    document.body.appendChild(loadingOverlay);
                    
                    window.location.reload();
                }, 1000);
            } else {
                this.showAlert('error', result.message);
            }
        } catch (error) {
            console.error('Error:', error);
            this.showAlert('error', 'An unexpected error occurred. Please try again.');
        } finally {
            // Reset button state
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
            submitBtn.disabled = false;
        }
    }

    showAlert(type, message) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert.auto-alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show auto-alert" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Insert alert at top of main content
        const mainContent = document.querySelector('main.container-fluid');
        if (mainContent) {
            mainContent.insertAdjacentHTML('afterbegin', alertHtml);
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                const alert = document.querySelector('.alert.auto-alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
    }

    displayValidationErrors(form, errors) {
        // Clear previous errors
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());

        // Display new errors
        Object.keys(errors).forEach(field => {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('is-invalid');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = errors[field];
                input.parentNode.appendChild(errorDiv);
            }
        });
    }
}

// Initialize membership manager when DOM is loaded
const membershipManager = new MembershipManager();

// Legacy functions for backward compatibility
function editMembership(membershipId) {
    const membership = membershipRecords.find(m => m.id == membershipId);
    if (!membership) return;
    
    const form = document.getElementById('editMembershipForm');
    form.action = `${baseUrl}agency/onboarding/${employeeId}/memberships/${membershipId}/update`;
    
    // Populate form fields
    const modalBody = form.querySelector('.modal-body');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label for="edit_professional_affiliation" class="form-label">Professional Affiliation <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_professional_affiliation" name="professional_affiliation" 
                           value="${membership.professional_affiliation}" required>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="edit_license_number" class="form-label">License/Member Number</label>
                    <input type="text" class="form-control" id="edit_license_number" name="license_number" 
                           value="${membership.license_number || ''}">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_current_status" class="form-label">Current Status <span class="text-danger">*</span></label>
                    <select class="form-select" id="edit_current_status" name="current_status" required>
                        <option value="active" ${membership.current_status === 'active' ? 'selected' : ''}>Active</option>
                        <option value="expired" ${membership.current_status === 'expired' ? 'selected' : ''}>Expired</option>
                        <option value="suspended" ${membership.current_status === 'suspended' ? 'selected' : ''}>Suspended</option>
                        <option value="pending" ${membership.current_status === 'pending' ? 'selected' : ''}>Pending</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_renewal_date" class="form-label">Renewal Date</label>
                    <input type="date" class="form-control" id="edit_renewal_date" name="renewal_date" 
                           value="${membership.renewal_date || ''}">
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label for="edit_document" class="form-label">Certificate/License Document</label>
            <input type="file" class="form-control" id="edit_document" name="document" 
                   accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
            <div class="form-text">
                ${membership.document_path ? 'Current document will be replaced if new file is uploaded.' : 'No document currently attached.'}
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('editMembershipModal')).show();
}

function deleteMembership(membershipId) {
    const form = document.getElementById('deleteMembershipForm');
    form.action = `${baseUrl}agency/onboarding/${employeeId}/memberships/${membershipId}/delete`;
    
    new bootstrap.Modal(document.getElementById('deleteMembershipModal')).show();
}
</script>
<?= $this->endSection() ?>
