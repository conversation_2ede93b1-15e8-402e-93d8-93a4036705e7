<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-light">Admin User Details</h1>
        <p class="text-muted mb-0">View admin user information and permissions</p>
    </div>
    <div>
        <a href="<?= base_url('dakoii/admin-users/' . $adminUser['id'] . '/edit') ?>" class="btn btn-warning me-2">
            <i class="bi bi-pencil me-2"></i>
            Edit User
        </a>
        <a href="<?= base_url('dakoii/admin-users') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to List
        </a>
    </div>
</div>

<!-- User Details -->
<div class="row">
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person me-2"></i>
                    Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Full Name</label>
                            <div class="h5 text-light">
                                <?= esc($adminUser['first_name'] . ' ' . $adminUser['last_name']) ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Username</label>
                            <div class="font-monospace text-light">
                                <?= esc($adminUser['username']) ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Email Address</label>
                            <div>
                                <a href="mailto:<?= esc($adminUser['email']) ?>" class="text-decoration-none">
                                    <?= esc($adminUser['email']) ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Phone Number</label>
                            <div class="text-light">
                                <?php if (!empty($adminUser['phone_number'])): ?>
                                    <a href="tel:<?= esc($adminUser['phone_number']) ?>" class="text-decoration-none">
                                        <?= esc($adminUser['phone_number']) ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Role</label>
                            <div>
                                <?php
                                $roleClass = 'bg-secondary';
                                $roleLabel = ucfirst($adminUser['role']);
                                if ($adminUser['role'] === 'admin') $roleClass = 'bg-danger';
                                elseif ($adminUser['role'] === 'ceo') $roleClass = 'bg-dark';
                                elseif ($adminUser['role'] === 'manager') $roleClass = 'bg-warning';
                                elseif ($adminUser['role'] === 'user') $roleClass = 'bg-info';
                                ?>
                                <span class="badge <?= $roleClass ?> fs-6">
                                    <?= esc($roleLabel) ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Status</label>
                            <div>
                                <?php if ($adminUser['is_active']): ?>
                                    <span class="badge bg-success fs-6">
                                        <i class="bi bi-check-circle me-1"></i>
                                        Active
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary fs-6">
                                        <i class="bi bi-x-circle me-1"></i>
                                        Inactive
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Role Permissions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-shield-check me-2"></i>
                    Role Permissions
                </h5>
            </div>
            <div class="card-body">
                <?php
                $permissions = [];
                switch ($adminUser['role']) {
                    case 'admin':
                        $permissions = [
                            'Full system administration access',
                            'Manage all admin users',
                            'Manage all agencies',
                            'View and generate all reports',
                            'System configuration and settings',
                            'Audit log access'
                        ];
                        break;
                    case 'ceo':
                        $permissions = [
                            'Executive oversight and strategic management',
                            'Access to all organizational data',
                            'Advanced analytics and reporting',
                            'System-wide configuration access',
                            'Full audit and compliance access',
                            'Strategic planning and decision making'
                        ];
                        break;
                    case 'manager':
                        $permissions = [
                            'Regional oversight and management',
                            'View assigned agencies',
                            'Generate regional reports',
                            'Manage assigned staff',
                            'Limited system settings access'
                        ];
                        break;
                    case 'user':
                        $permissions = [
                            'Basic data entry access',
                            'View assigned agency data',
                            'Generate basic reports',
                            'Update personal profile'
                        ];
                        break;
                }
                ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">Current Role: <?= ucfirst(esc($adminUser['role'])) ?></h6>
                        <ul class="list-unstyled">
                            <?php foreach ($permissions as $permission): ?>
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <?= esc($permission) ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Note:</strong> Permissions are automatically assigned based on the user's role. 
                            To change permissions, update the user's role.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Activity Log (Placeholder) -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-activity me-2"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="bi bi-clock-history display-4 text-muted"></i>
                    <h6 class="mt-3 text-muted">Activity Tracking</h6>
                    <p class="text-muted">Activity logging will be implemented in future updates.</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= base_url('dakoii/admin-users/' . $adminUser['id'] . '/edit') ?>" class="btn btn-warning">
                        <i class="bi bi-pencil me-2"></i>
                        Edit User
                    </a>
                    
                    <?php if ($adminUser['is_active']): ?>
                    <form method="POST" action="<?= base_url('dakoii/admin-users/' . $adminUser['id'] . '/update') ?>">
                        <?= csrf_field() ?>
                        <input type="hidden" name="username" value="<?= esc($adminUser['username']) ?>">
                        <input type="hidden" name="email" value="<?= esc($adminUser['email']) ?>">
                        <input type="hidden" name="first_name" value="<?= esc($adminUser['first_name']) ?>">
                        <input type="hidden" name="last_name" value="<?= esc($adminUser['last_name']) ?>">
                        <input type="hidden" name="phone_number" value="<?= esc($adminUser['phone_number']) ?>">
                        <input type="hidden" name="role" value="<?= esc($adminUser['role']) ?>">
                        <input type="hidden" name="is_active" value="0">
                        <button type="submit" class="btn btn-outline-secondary w-100" 
                                onclick="return confirm('Are you sure you want to deactivate this user?')">
                            <i class="bi bi-pause-circle me-2"></i>
                            Deactivate User
                        </button>
                    </form>
                    <?php else: ?>
                    <form method="POST" action="<?= base_url('dakoii/admin-users/' . $adminUser['id'] . '/update') ?>">
                        <?= csrf_field() ?>
                        <input type="hidden" name="username" value="<?= esc($adminUser['username']) ?>">
                        <input type="hidden" name="email" value="<?= esc($adminUser['email']) ?>">
                        <input type="hidden" name="first_name" value="<?= esc($adminUser['first_name']) ?>">
                        <input type="hidden" name="last_name" value="<?= esc($adminUser['last_name']) ?>">
                        <input type="hidden" name="phone_number" value="<?= esc($adminUser['phone_number']) ?>">
                        <input type="hidden" name="role" value="<?= esc($adminUser['role']) ?>">
                        <input type="hidden" name="is_active" value="1">
                        <button type="submit" class="btn btn-outline-success w-100">
                            <i class="bi bi-play-circle me-2"></i>
                            Activate User
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- User Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    User Statistics
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Account Created</small>
                    <div class="fw-bold"><?= date('M j, Y', strtotime($adminUser['created_at'])) ?></div>
                    <small class="text-muted"><?= date('g:i A', strtotime($adminUser['created_at'])) ?></small>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Last Updated</small>
                    <div class="fw-bold"><?= date('M j, Y', strtotime($adminUser['updated_at'])) ?></div>
                    <small class="text-muted"><?= date('g:i A', strtotime($adminUser['updated_at'])) ?></small>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Account Age</small>
                    <div class="fw-bold">
                        <?php
                        $created = new DateTime($adminUser['created_at']);
                        $now = new DateTime();
                        $diff = $now->diff($created);
                        echo $diff->days . ' days';
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Danger Zone -->
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Danger Zone
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted small">
                    Permanently delete this admin user. This action cannot be undone.
                </p>
                <form method="POST" 
                      action="<?= base_url('dakoii/admin-users/' . $adminUser['id'] . '/delete') ?>" 
                      onsubmit="return confirm('Are you sure you want to delete this admin user? This action cannot be undone.')">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger btn-sm">
                        <i class="bi bi-trash me-2"></i>
                        Delete User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
