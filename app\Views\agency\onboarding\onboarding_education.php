<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<style>
/* Mobile-friendly action buttons */
@media (max-width: 767.98px) {
    .action-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.25rem;
        min-width: 45px;
    }
    .action-btn i {
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
    }
    .action-btn span {
        font-size: 0.65rem;
        line-height: 1;
    }
}

@media (min-width: 768px) {
    .action-btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.5rem;
    }
    .action-btn i {
        font-size: 1rem;
    }
    .action-btn span {
        font-size: 0.75rem;
    }
}
</style>

<!-- Navigation Buttons -->
<div class="row mb-4">
    <div class="col-6">
        <a href="<?= base_url('agency/onboarding') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Employee List
        </a>
    </div>
    <div class="col-6 text-end">
        <!-- Additional actions can be added here -->
    </div>
</div>

<!-- Onboarding Steps Navigation -->
<div class="row mb-4">
    <div class="col-md-3 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/profile') ?>" class="btn btn-outline-primary w-100">
            <i class="bi bi-person"></i><br>
            <small>Personal Info</small>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <button class="btn btn-info w-100" disabled>
            <i class="bi bi-mortarboard"></i><br>
            <small>Education</small>
        </button>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/memberships') ?>" class="btn btn-outline-success w-100">
            <i class="bi bi-award"></i><br>
            <small>Memberships</small>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/files') ?>" class="btn btn-outline-warning w-100">
            <i class="bi bi-file-earmark"></i><br>
            <small>Files</small>
        </a>
    </div>
    <div class="col-md-3 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/employment-history') ?>" class="btn btn-outline-warning w-100">
            <i class="bi bi-briefcase"></i><br>
            <small>Employment</small>
        </a>
    </div>
</div>

<!-- Page Header -->
<div class="text-center mb-4">
    <h2 class="mb-1">
        <i class="bi bi-person-circle text-primary"></i>
        <?= esc($employee['first_name'] . ' ' . $employee['last_name']) ?>
    </h2>
    <p class="text-muted mb-0">
        <?php if (!empty($employee['employment_number'])): ?>
            File Number: <?= esc($employee['employment_number']) ?> •
        <?php endif; ?>
        Education & Qualifications
    </p>
</div>

<!-- Education Stats Row -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary"><?= $stats['basic_qualifications'] ?></h4>
                <p class="text-muted mb-0">Basic</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info"><?= $stats['additional_qualifications'] ?></h4>
                <p class="text-muted mb-0">Additional</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success"><?= count($education_records) ?></h4>
                <p class="text-muted mb-0">Total Records</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning"><?= array_sum(array_column($education_records, 'completion_year')) > 0 ? count(array_filter($education_records, function($e) { return !empty($e['completion_year']) && $e['completion_year'] >= (date('Y') - 5); })) : 0 ?></h4>
                <p class="text-muted mb-0">Recent</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Education Content -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-mortarboard text-info"></i>
            Education & Qualifications
        </h5>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEducationModal">
                <i class="bi bi-plus-circle"></i> Add Education
            </button>
        </div>
    </div>
            <div class="card-body">
                <?php if (empty($education_records)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-mortarboard text-muted" style="font-size: 4rem;"></i>
                        <h5 class="text-muted mt-3">No education records found</h5>
                        <p class="text-muted">Start by adding the employee's educational qualifications.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEducationModal">
                            <i class="bi bi-plus-circle"></i> Add First Education Record
                        </button>
                    </div>
                <?php else: ?>
                    <!-- Education Records Table -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" width="18%">
                                        <i class="bi bi-mortarboard text-primary"></i> Qualification
                                    </th>
                                    <th scope="col" width="10%">
                                        <i class="bi bi-tag text-info"></i> Type
                                    </th>
                                    <th scope="col" width="15%">
                                        <i class="bi bi-book text-primary"></i> Course
                                    </th>
                                    <th scope="col" width="12%">
                                        <i class="bi bi-list-ul text-secondary"></i> Units
                                    </th>
                                    <th scope="col" width="15%">
                                        <i class="bi bi-building text-secondary"></i> Institution
                                    </th>
                                    <th scope="col" width="8%">
                                        <i class="bi bi-calendar-check text-success"></i> Year
                                    </th>
                                    <th scope="col" width="10%">
                                        <i class="bi bi-award text-warning"></i> Certificate
                                    </th>
                                    <th scope="col" width="5%">
                                        <i class="bi bi-file-earmark text-info"></i> Doc
                                    </th>
                                    <th scope="col" width="12%">
                                        <i class="bi bi-gear text-muted"></i> Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($education_records as $education): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-semibold text-dark">
                                                <?= esc($education['qualification_name']) ?>
                                            </div>
                                            <?php if (!empty($education['course_duration'])): ?>
                                                <small class="text-muted">
                                                    <i class="bi bi-clock"></i> <?= esc($education['course_duration']) ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="text-dark">
                                                <?= ucfirst($education['qualification_type']) ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-dark">
                                                <?= esc($education['course_taken']) ?: '<span class="text-muted">-</span>' ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-dark">
                                                <?php if (!empty($education['units'])): ?>
                                                    <small class="text-dark">
                                                        <?= esc(substr($education['units'], 0, 50)) ?><?= strlen($education['units']) > 50 ? '...' : '' ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-dark">
                                                <?= esc($education['institution']) ?: '<span class="text-muted">-</span>' ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <?php if (!empty($education['completion_year'])): ?>
                                                    <span class="text-dark">
                                                        <?= esc($education['completion_year']) ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <?php if (!empty($education['certificate_number'])): ?>
                                                    <small class="text-dark fw-medium">
                                                        <?= esc($education['certificate_number']) ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <?php if (!empty($education['document_path'])): ?>
                                                    <a href="<?= base_url($education['document_path']) ?>" target="_blank"
                                                       class="btn btn-sm btn-outline-success" title="View Document">
                                                        <i class="bi bi-file-earmark-check"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">
                                                        <i class="bi bi-file-earmark-x"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="row g-1">
                                                <div class="col-6">
                                                    <button class="btn btn-outline-primary w-100 action-btn" type="button"
                                                            onclick="editEducation(<?= $education['id'] ?>)"
                                                            title="Edit Education">
                                                        <i class="bi bi-pencil d-block d-md-inline"></i>
                                                        <span class="d-block d-md-none">Edit</span>
                                                    </button>
                                                </div>
                                                <div class="col-6">
                                                    <button class="btn btn-outline-danger w-100 action-btn" type="button"
                                                            onclick="deleteEducation(<?= $education['id'] ?>)"
                                                            title="Delete Education">
                                                        <i class="bi bi-trash d-block d-md-inline"></i>
                                                        <span class="d-block d-md-none">Delete</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Table Summary -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i>
                                Showing <?= count($education_records) ?> education record<?= count($education_records) !== 1 ? 's' : '' ?>
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                <span class="fw-medium"><?= $stats['basic_qualifications'] ?></span> Basic,
                                <span class="fw-medium"><?= $stats['additional_qualifications'] ?></span> Additional
                            </small>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Education Modal -->
<div class="modal fade" id="addEducationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Education Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="<?= base_url('agency/onboarding/' . $employee['id'] . '/education/store') ?>" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <input type="hidden" id="qualification_id" name="qualification_id" value="">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="qualification_type" class="form-label">Qualification Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="qualification_type" name="qualification_type" required>
                                    <option value="">Select Type</option>
                                    <option value="basic">Basic Education</option>
                                    <option value="additional">Additional Qualification</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="qualification_name" class="form-label">Qualification Name <span class="text-danger">*</span></label>
                                <select class="form-select" id="qualification_name" name="qualification_name" required>
                                    <option value="">Select Qualification</option>
                                    <?php if (!empty($qualifications)): ?>
                                        <?php foreach ($qualifications as $groupName => $groupQualifications): ?>
                                            <optgroup label="<?= esc($groupName) ?>">
                                                <?php foreach ($groupQualifications as $qualId => $qualName): ?>
                                                    <option value="<?= esc($qualName) ?>" data-id="<?= $qualId ?>"><?= esc($qualName) ?></option>
                                                <?php endforeach; ?>
                                            </optgroup>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                    <option value="other">Other (specify below)</option>
                                </select>
                                <input type="text" class="form-control mt-2" id="custom_qualification_name" name="custom_qualification_name"
                                       placeholder="Enter custom qualification name" style="display: none;">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="course_taken" class="form-label">Course Taken</label>
                                <input type="text" class="form-control" id="course_taken" name="course_taken"
                                       placeholder="e.g., Bachelor of Nursing, Diploma in Health Sciences">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="units" class="form-label">Units/Subjects</label>
                                <textarea class="form-control" id="units" name="units" rows="3"
                                          placeholder="List the main units or subjects covered in this course"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="institution_name" class="form-label">Institution Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="institution_name" name="institution_name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="completion_year" class="form-label">Year Completed</label>
                                <input type="number" class="form-control" id="completion_year" name="completion_year"
                                       min="1950" max="<?= date('Y') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="certificate_number" class="form-label">Certificate Number</label>
                                <input type="text" class="form-control" id="certificate_number" name="certificate_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document" class="form-label">Certificate/Document</label>
                                <input type="file" class="form-control" id="document" name="document" 
                                       accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                <div class="form-text">Accepted formats: PDF, JPG, PNG, DOC, DOCX (Max: 5MB)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Education Record</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Education Modal -->
<div class="modal fade" id="editEducationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Education Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editEducationForm" method="POST" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <!-- Form fields will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Education Record</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteEducationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this education record? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteEducationForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Education records data for JavaScript
const educationRecords = <?= json_encode($education_records) ?>;

function editEducation(educationId) {
    const education = educationRecords.find(e => e.id == educationId);
    if (!education) return;
    
    const form = document.getElementById('editEducationForm');
    form.action = `<?= base_url('agency/onboarding/' . $employee['id'] . '/education/') ?>${educationId}/update`;
    
    // Populate form fields
    const modalBody = form.querySelector('.modal-body');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_qualification_type" class="form-label">Qualification Type <span class="text-danger">*</span></label>
                    <select class="form-select" id="edit_qualification_type" name="qualification_type" required>
                        <option value="basic" ${education.qualification_type === 'basic' ? 'selected' : ''}>Basic Education</option>
                        <option value="additional" ${education.qualification_type === 'additional' ? 'selected' : ''}>Additional Qualification</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_qualification_name" class="form-label">Qualification Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_qualification_name" name="qualification_name"
                           value="${education.qualification_name || ''}" required>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_course_taken" class="form-label">Course Taken</label>
                    <input type="text" class="form-control" id="edit_course_taken" name="course_taken"
                           value="${education.course_taken || ''}" placeholder="e.g., Bachelor of Nursing, Diploma in Health Sciences">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_units" class="form-label">Units/Subjects</label>
                    <textarea class="form-control" id="edit_units" name="units" rows="3"
                              placeholder="List the main units or subjects covered in this course">${education.units || ''}</textarea>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label for="edit_institution" class="form-label">Institution Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_institution" name="institution"
                           value="${education.institution || ''}" required>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="edit_completion_year" class="form-label">Year Completed</label>
                    <input type="number" class="form-control" id="edit_completion_year" name="completion_year"
                           value="${education.completion_year || ''}" min="1950" max="<?= date('Y') ?>">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_certificate_number" class="form-label">Certificate Number</label>
                    <input type="text" class="form-control" id="edit_certificate_number" name="certificate_number"
                           value="${education.certificate_number || ''}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_document" class="form-label">Certificate/Document</label>
                    <input type="file" class="form-control" id="edit_document" name="document"
                           accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                    <div class="form-text">
                        ${education.document_path ? 'Current document will be replaced if new file is uploaded.' : 'No document currently attached.'}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('editEducationModal')).show();
}

function deleteEducation(educationId) {
    const form = document.getElementById('deleteEducationForm');
    form.action = `<?= base_url('agency/onboarding/' . $employee['id'] . '/education/') ?>${educationId}/delete`;

    new bootstrap.Modal(document.getElementById('deleteEducationModal')).show();
}

// Handle qualification dropdown changes
document.addEventListener('DOMContentLoaded', function() {
    const qualificationSelect = document.getElementById('qualification_name');
    const customInput = document.getElementById('custom_qualification_name');
    const qualificationIdInput = document.getElementById('qualification_id');

    if (qualificationSelect) {
        qualificationSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];

            if (this.value === 'other') {
                // Show custom input for "Other" option
                customInput.style.display = 'block';
                customInput.required = true;
                qualificationIdInput.value = '';
            } else {
                // Hide custom input and set qualification_id
                customInput.style.display = 'none';
                customInput.required = false;
                customInput.value = '';

                // Get qualification ID from data attribute
                const qualId = selectedOption.getAttribute('data-id');
                qualificationIdInput.value = qualId || '';
            }
        });
    }
});
</script>

<?= $this->endSection() ?>
