<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdateAdminUsersRoleEnum extends Migration
{
    public function up()
    {
        // First, update any existing 'supervisor' records to 'manager'
        $this->db->query("UPDATE admin_users SET role = 'manager' WHERE role = 'supervisor'");
        
        // Then alter the ENUM to include the new values
        // Note: MySQL requires us to specify all ENUM values when altering
        $this->forge->modifyColumn('admin_users', [
            'role' => [
                'type' => 'ENUM',
                'constraint' => ['user', 'admin', 'manager', 'ceo'],
                'default' => 'user',
            ]
        ]);
    }

    public function down()
    {
        // First, update any 'manager' records back to 'supervisor'
        $this->db->query("UPDATE admin_users SET role = 'supervisor' WHERE role = 'manager'");
        
        // Update any 'ceo' records to 'admin' (since CEO didn't exist before)
        $this->db->query("UPDATE admin_users SET role = 'admin' WHERE role = 'ceo'");
        
        // Then revert the ENUM to the original values
        $this->forge->modifyColumn('admin_users', [
            'role' => [
                'type' => 'ENUM',
                'constraint' => ['supervisor', 'user', 'admin'],
                'default' => 'user',
            ]
        ]);
    }
}
