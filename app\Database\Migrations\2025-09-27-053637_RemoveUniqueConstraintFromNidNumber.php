<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class RemoveUniqueConstraintFromNidNumber extends Migration
{
    public function up()
    {
        // Remove unique constraints from employment_number and nid_number fields
        $this->forge->dropKey('employee_personal_information', 'employment_number');
        $this->forge->dropKey('employee_personal_information', 'nid_number');
    }

    public function down()
    {
        // Add back unique constraints if needed to rollback
        $this->forge->addUniqueKey('employment_number');
        $this->forge->addUniqueKey('nid_number');
        $this->forge->processIndexes('employee_personal_information');
    }
}
