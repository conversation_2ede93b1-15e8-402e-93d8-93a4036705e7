<?php

namespace App\Models;

use CodeIgniter\Model;

class EmployeeEmploymentHistoryModel extends Model
{
    protected $table = 'employee_employment_history';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'employee_id',
        'employer_name',
        'designation',
        'employment_duration',
        'start_date',
        'end_date',
        'is_current'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'employee_id' => 'required|integer',
        'employer_name' => 'required|min_length[2]|max_length[255]',
        'designation' => 'permit_empty|max_length[100]',
        'employment_duration' => 'permit_empty|max_length[50]',
        'start_date' => 'permit_empty|valid_date',
        'end_date' => 'permit_empty|valid_date',
        'is_current' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'employer_name' => [
            'required' => 'Employer name is required.',
            'min_length' => 'Employer name must be at least 2 characters long.'
        ],
        'start_date' => [
            'valid_date' => 'Please enter a valid start date.'
        ],
        'end_date' => [
            'valid_date' => 'Please enter a valid end date.'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Get employment history by employee ID
     */
    public function getHistoryByEmployee(int $employeeId): array
    {
        return $this->where('employee_id', $employeeId)
                   ->orderBy('is_current', 'DESC')
                   ->orderBy('start_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get current employment for employee
     */
    public function getCurrentEmployment(int $employeeId): ?array
    {
        return $this->where(['employee_id' => $employeeId, 'is_current' => 1])
                   ->first();
    }

    /**
     * Get previous employment history (excluding current)
     */
    public function getPreviousEmployment(int $employeeId): array
    {
        return $this->where(['employee_id' => $employeeId, 'is_current' => 0])
                   ->orderBy('start_date', 'DESC')
                   ->findAll();
    }

    /**
     * Calculate total work experience in years
     */
    public function getTotalExperienceYears(int $employeeId): float
    {
        $history = $this->getHistoryByEmployee($employeeId);
        $totalDays = 0;
        
        foreach ($history as $employment) {
            if (!empty($employment['start_date'])) {
                $startDate = new \DateTime($employment['start_date']);
                
                if (!empty($employment['end_date'])) {
                    $endDate = new \DateTime($employment['end_date']);
                } else {
                    $endDate = new \DateTime(); // Current date for ongoing employment
                }
                
                $interval = $startDate->diff($endDate);
                $totalDays += $interval->days;
            }
        }
        
        return round($totalDays / 365.25, 1); // Convert to years
    }

    /**
     * Get employment statistics for employee
     */
    public function getEmploymentStats(int $employeeId): array
    {
        $total = $this->where('employee_id', $employeeId)->countAllResults();
        $current = $this->where(['employee_id' => $employeeId, 'is_current' => 1])->countAllResults();
        $previous = $this->where(['employee_id' => $employeeId, 'is_current' => 0])->countAllResults();
        $totalExperience = $this->getTotalExperienceYears($employeeId);
        
        return [
            'total_employments' => $total,
            'current_employment' => $current,
            'previous_employments' => $previous,
            'total_experience_years' => $totalExperience
        ];
    }

    /**
     * Set current employment (unset others first)
     */
    public function setCurrentEmployment(int $employeeId, int $employmentId): bool
    {
        // First, unset all current employments for this employee
        $this->where('employee_id', $employeeId)->set(['is_current' => 0])->update();
        
        // Then set the specified employment as current
        return $this->update($employmentId, ['is_current' => 1, 'end_date' => null]);
    }

    /**
     * End current employment
     */
    public function endCurrentEmployment(int $employeeId, string $endDate = null): bool
    {
        if (!$endDate) {
            $endDate = date('Y-m-d');
        }
        
        return $this->where(['employee_id' => $employeeId, 'is_current' => 1])
                   ->set(['is_current' => 0, 'end_date' => $endDate])
                   ->update();
    }

    /**
     * Calculate employment duration automatically
     */
    public function calculateDuration(string $startDate, string $endDate = null): string
    {
        if (empty($startDate)) {
            return '';
        }
        
        $start = new \DateTime($startDate);
        $end = $endDate ? new \DateTime($endDate) : new \DateTime();
        
        $interval = $start->diff($end);
        
        $years = $interval->y;
        $months = $interval->m;
        
        $duration = '';
        if ($years > 0) {
            $duration .= $years . ' year' . ($years > 1 ? 's' : '');
        }
        if ($months > 0) {
            if ($duration) $duration .= ', ';
            $duration .= $months . ' month' . ($months > 1 ? 's' : '');
        }
        
        return $duration ?: 'Less than 1 month';
    }

    /**
     * Before insert/update callback to calculate duration
     */
    protected function beforeInsert(array $data): array
    {
        if (isset($data['data']['start_date'])) {
            $data['data']['employment_duration'] = $this->calculateDuration(
                $data['data']['start_date'],
                $data['data']['end_date'] ?? null
            );
        }
        return $data;
    }

    protected function beforeUpdate(array $data): array
    {
        if (isset($data['data']['start_date'])) {
            $data['data']['employment_duration'] = $this->calculateDuration(
                $data['data']['start_date'],
                $data['data']['end_date'] ?? null
            );
        }
        return $data;
    }

    /**
     * Get employment history with employee details for agency
     */
    public function getHistoryWithEmployeeByAgency(int $agencyId): array
    {
        return $this->select('employee_employment_history.*, employee_personal_information.first_name, employee_personal_information.last_name, employee_personal_information.employment_number')
                   ->join('employee_personal_information', 'employee_personal_information.id = employee_employment_history.employee_id')
                   ->where('employee_personal_information.agency_id', $agencyId)
                   ->orderBy('employee_employment_history.is_current', 'DESC')
                   ->orderBy('employee_employment_history.start_date', 'DESC')
                   ->findAll();
    }

    /**
     * Validate date range
     */
    public function validateDateRange(string $startDate, string $endDate = null): bool
    {
        if (empty($startDate)) {
            return false;
        }
        
        if ($endDate && $endDate < $startDate) {
            return false;
        }
        
        return true;
    }
}
