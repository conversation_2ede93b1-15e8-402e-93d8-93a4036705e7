<?php

namespace App\Controllers\Agency;

use App\Controllers\BaseController;
use App\Libraries\AdminAuth;
use App\Models\AgencyModel;

class AgencyBaseController extends BaseController
{
    protected $auth;
    protected $session;
    protected $agencyModel;
    protected $currentAgency;
    protected $pageTitle = '';
    protected $breadcrumbs = [];

    public function __construct()
    {
        $this->auth = new AdminAuth();
        $this->session = \Config\Services::session();
        $this->agencyModel = new AgencyModel();
        
        // Load current agency information
        $this->loadCurrentAgency();
    }

    /**
     * Load current agency information
     */
    protected function loadCurrentAgency()
    {
        if ($this->isAuthenticated()) {
            $agencyId = $this->auth->agencyId();
            if ($agencyId) {
                $this->currentAgency = $this->agencyModel->find($agencyId);
            }
        }
    }

    /**
     * Check if user is authenticated and has agency access
     */
    protected function isAuthenticated(): bool
    {
        if (!$this->auth->check()) {
            return false;
        }

        // Validate session timeout
        if (!$this->auth->validateSession()) {
            return false;
        }

        // Check if user has agency assignment
        $agencyId = $this->auth->agencyId();
        if (empty($agencyId)) {
            return false;
        }

        return true;
    }

    /**
     * Require authentication - redirect to login if not authenticated
     */
    protected function requireAuth()
    {
        if (!$this->isAuthenticated()) {
            // Store intended URL
            $this->session->set('admin_intended_url', current_url());
            
            // Set flash message
            $this->setFlashMessage('error', 'Please log in to access the Agency Portal.');
            
            // Redirect to login
            return redirect()->to('/admin/login');
        }
        
        return null;
    }

    /**
     * Check if user has permission for specific action
     */
    protected function hasPermission(string $action, string $resource = ''): bool
    {
        return $this->auth->hasPermission($action, $resource);
    }

    /**
     * Require specific permission
     */
    protected function requirePermission(string $action, string $resource = '')
    {
        if (!$this->hasPermission($action, $resource)) {
            $this->setFlashMessage('error', 'You do not have permission to perform this action.');
            return redirect()->to('/agency/dashboard');
        }
        
        return null;
    }

    /**
     * Get current user data
     */
    protected function getCurrentUser(): ?array
    {
        return $this->auth->user();
    }

    /**
     * Get current agency data
     */
    protected function getCurrentAgency(): ?array
    {
        return $this->currentAgency;
    }

    /**
     * Get current agency ID
     */
    protected function getCurrentAgencyId(): ?int
    {
        return $this->currentAgency ? $this->currentAgency['id'] : null;
    }

    /**
     * Set page title
     */
    protected function setPageTitle(string $title)
    {
        $this->pageTitle = $title;
    }

    /**
     * Add breadcrumb
     */
    protected function addBreadcrumb(string $title, string $url = '')
    {
        $this->breadcrumbs[] = [
            'title' => $title,
            'url' => $url
        ];
    }

    /**
     * Set flash message
     */
    protected function setFlashMessage(string $type, string $message)
    {
        $this->session->setFlashdata('flash_' . $type, $message);
    }

    /**
     * Get flash messages
     */
    protected function getFlashMessages(): array
    {
        return [
            'success' => $this->session->getFlashdata('flash_success'),
            'error' => $this->session->getFlashdata('flash_error'),
            'warning' => $this->session->getFlashdata('flash_warning'),
            'info' => $this->session->getFlashdata('flash_info')
        ];
    }

    /**
     * Prepare common view data
     */
    protected function getViewData(array $additionalData = []): array
    {
        $baseData = [
            'pageTitle' => $this->pageTitle,
            'breadcrumbs' => $this->breadcrumbs,
            'currentUser' => $this->getCurrentUser(),
            'currentAgency' => $this->getCurrentAgency(),
            'flashMessages' => $this->getFlashMessages(),
            'auth' => $this->auth
        ];

        return array_merge($baseData, $additionalData);
    }

    /**
     * Render view with common data
     */
    protected function renderView(string $view, array $data = []): string
    {
        return view($view, $this->getViewData($data));
    }

    /**
     * Check if employee belongs to current agency
     */
    protected function employeeBelongsToAgency(int $employeeId): bool
    {
        $employeeModel = new \App\Models\EmployeeModel();
        return $employeeModel->belongsToAgency($employeeId, $this->getCurrentAgencyId());
    }

    /**
     * Validate employee access
     */
    protected function validateEmployeeAccess(int $employeeId)
    {
        if (!$this->employeeBelongsToAgency($employeeId)) {
            $this->setFlashMessage('error', 'You do not have access to this employee.');
            return redirect()->to('/agency/employees');
        }
        
        return null;
    }

    /**
     * Handle file upload
     */
    protected function handleFileUpload($file, string $uploadPath = 'uploads/documents/'): ?array
    {
        if (!$file->isValid()) {
            return null;
        }

        // Validate file type
        $allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
        if (!in_array($file->getExtension(), $allowedTypes)) {
            return null;
        }

        // Validate file size (10MB max)
        if ($file->getSize() > 10 * 1024 * 1024) {
            return null;
        }

        // Generate unique filename
        $newName = $file->getRandomName();
        
        // Create upload directory if it doesn't exist
        $fullPath = WRITEPATH . $uploadPath;
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // Move file
        if ($file->move($fullPath, $newName)) {
            return [
                'original_name' => $file->getName(),
                'new_name' => $newName,
                'file_path' => $uploadPath . $newName,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];
        }

        return null;
    }

    /**
     * Delete uploaded file
     */
    protected function deleteUploadedFile(string $filePath): bool
    {
        $fullPath = WRITEPATH . $filePath;
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        return false;
    }

    /**
     * Format date for display
     */
    protected function formatDate(?string $date, string $format = 'M d, Y'): string
    {
        if (empty($date)) {
            return '-';
        }
        
        return date($format, strtotime($date));
    }

    /**
     * Format datetime for display
     */
    protected function formatDateTime(?string $datetime, string $format = 'M d, Y g:i A'): string
    {
        if (empty($datetime)) {
            return '-';
        }
        
        return date($format, strtotime($datetime));
    }

    /**
     * Generate employee number
     */
    protected function generateEmployeeNumber(): string
    {
        $employeeModel = new \App\Models\EmployeeModel();
        return $employeeModel->generateUniqueEmployeeNumber($this->getCurrentAgencyId());
    }
}
