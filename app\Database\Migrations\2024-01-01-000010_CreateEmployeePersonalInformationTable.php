<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateEmployeePersonalInformationTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'employment_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'agency_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'nid_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'first_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'middle_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'last_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'email_address' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'gender' => [
                'type' => 'ENUM',
                'constraint' => ['male', 'female'],
            ],
            'date_of_birth' => [
                'type' => 'DATE',
                'null' => true,
            ],
            'date_of_commencement' => [
                'type' => 'DATE',
                'null' => true,
            ],
            'marital_status' => [
                'type' => 'ENUM',
                'constraint' => ['single', 'married', 'divorced', 'widowed'],
                'null' => true,
            ],
            'spouse_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'number_of_children' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'home_province' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'home_district' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'home_village' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'mobile_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'emergency_contact_person' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'emergency_contact_phone' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'designation' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'department' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            
            // Banking Details
            'bank_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'bank_branch' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'account_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'account_number' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'account_type' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            
            // NASFUND Details
            'nasfund_member_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'nasfund_membership_number' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'nasfund_year_joined' => [
                'type' => 'YEAR',
                'null' => true,
            ],
            'nasfund_branch' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            
            // Process Fields
            'onboard_status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'in_progress', 'completed', 'rejected'],
                'default' => 'pending',
            ],
            'onboard_status_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'onboard_status_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'onboard_status_remarks' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            
            'hr_review_status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'approved', 'rejected'],
                'default' => 'pending',
            ],
            'hr_review_status_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'hr_review_status_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            
            'supervisor_review_status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'approved', 'rejected'],
                'default' => 'pending',
            ],
            'supervisor_review_status_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'supervisor_review_status_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            
            'ceo_review_status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'approved', 'rejected'],
                'default' => 'pending',
            ],
            'ceo_review_status_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'ceo_review_status_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            
            'employment_status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'active', 'inactive', 'terminated'],
                'default' => 'pending',
            ],
            'employment_status_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'employment_status_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            
            'public_profile_enabled' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
            ],
            'public_profile_token' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'public_profile_expires_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'updated_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('employment_number');
        $this->forge->addUniqueKey('nid_number');
        $this->forge->addUniqueKey('email_address');
        $this->forge->addUniqueKey('public_profile_token');
        $this->forge->addKey('agency_id');
        $this->forge->addKey('onboard_status');
        $this->forge->addKey('employment_status');
        $this->forge->addForeignKey('agency_id', 'agencies', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('employee_personal_information');
    }

    public function down()
    {
        $this->forge->dropTable('employee_personal_information');
    }
}
