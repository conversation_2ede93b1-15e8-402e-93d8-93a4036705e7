# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CHealth Wokman is a PHP-based employee management system built with CodeIgniter 4 for Christian Health Services PNG. It implements a multi-portal architecture with distinct interfaces for different organizational levels.

## Common Development Commands

### Testing
```bash
# Run all tests
vendor\bin\phpunit

# Run specific test directory
vendor\bin\phpunit tests/unit

# Generate code coverage
vendor\bin\phpunit --colors --coverage-text=tests/coverage.txt --coverage-html=tests/coverage/ -d memory_limit=1024m
```

### Database Operations
```bash
# Run migrations
php spark migrate

# Run seeder
php spark db:seed DakoiiUserSeeder

# Check migration status
php spark migrate:status

# Rollback migrations
php spark migrate:rollback
```

### Development Server
```bash
# Start development server
php spark serve

# With custom port
php spark serve --port=8080
```

## Architecture Overview

### Multi-Portal System
The application implements a 4-portal architecture:

1. **Dakoii Portal** (`/dakoii`) - Super administrator portal for system-wide management
2. **Admin Portal** (`/admin`) - Organization-level administration 
3. **Agency Portal** (`/agency`) - Individual health facility management
4. **Employee Portal** - Individual employee self-service (future implementation)

### Authentication System
- Portal-specific authentication filters: `DakoiiAuthFilter`, `AdminAuthFilter`, `AgencyAuthFilter`
- Custom authentication libraries: `DakoiiAuth`, `AdminAuth`
- Session-based authentication with role-based access control

### Database Models
- `DakoiiUserModel` - Super admin users
- `AdminUserModel` - Organization admin users  
- `AgencyModel` - Health facility/agency data
- Employee-related models: `EmployeePersonalInformationModel`, `EmployeeEducationModel`, `EmployeeEmploymentHistoryModel`, `EmployeeProfessionalMembershipModel`

### Controller Structure
Controllers are organized by portal with base controllers for shared functionality:
- `DakoiiBaseController` - Base for Dakoii portal controllers
- `AdminBaseController` - Base for Admin portal controllers
- `AgencyBaseController` - Base for Agency portal controllers

## Key Features

### Onboarding System
The agency portal includes a comprehensive employee onboarding workflow:
- Personal information management
- Education history tracking
- Professional membership management
- Employment history records
- Document upload and verification

### File Uploads
The system handles file uploads for:
- Education certificates (`public/uploads/education/`)
- Professional membership documents (`public/uploads/memberships/`)

## Development Setup

### Prerequisites
- PHP 8.1 or higher
- MySQL/MariaDB
- Composer
- XAMPP (for local development)

### Environment Configuration
Copy `env` to `.env` and configure:
- Database connection settings
- Base URL configuration
- Environment-specific settings

### Database Setup
For online database migration, refer to `migration_commands.md` which includes:
- SSH tunnel setup for remote database access
- Custom migration scripts
- Verification procedures

## Testing Configuration

### Database Testing
Configure test database in `app/Config/Database.php` or `.env` for the `tests` group. Tests include:
- Unit tests in `tests/unit/`
- Database tests with migration and seeding support
- Session tests in `tests/session/`

### Code Coverage
Requires XDebug installation with `xdebug.mode=coverage` in php.ini.

## Security Considerations

### Default Credentials
After seeding, default admin credentials are:
- Username: admin
- Email: <EMAIL>
- Password: admin123
- **Important**: Change password after first login

### File Structure
- Public assets in `public/` directory
- Application logic in `app/` directory
- Writable files in `writable/` directory
- Uploaded files in `public/uploads/` with proper subdirectories

## Current Implementation Status

The system is actively being developed with:
- Dakoii and Admin portals implemented
- Agency portal with onboarding functionality completed
- Employee portal planned for future implementation
- Branch: `onboarding-feature` contains latest employee onboarding implementation