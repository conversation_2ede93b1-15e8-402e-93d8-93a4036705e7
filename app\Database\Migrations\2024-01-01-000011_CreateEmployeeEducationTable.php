<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateEmployeeEducationTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'employee_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'qualification_type' => [
                'type' => 'ENUM',
                'constraint' => ['basic', 'additional'],
            ],
            'qualification_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'institution' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'course_duration' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'completion_year' => [
                'type' => 'YEAR',
                'null' => true,
            ],
            'certificate_number' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'is_verified' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
            ],
            'document_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey(['employee_id', 'qualification_type']);
        $this->forge->addForeignKey('employee_id', 'employee_personal_information', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('employee_education');
    }

    public function down()
    {
        $this->forge->dropTable('employee_education');
    }
}
