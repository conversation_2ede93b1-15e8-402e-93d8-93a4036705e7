<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class ModifyDateOfCommencementToAllowNull extends Migration
{
    public function up()
    {
        // Modify date_of_commencement field to allow NULL values
        $fields = [
            'date_of_commencement' => [
                'type' => 'DATE',
                'null' => true,
                'default' => null
            ]
        ];

        $this->forge->modifyColumn('employee_personal_information', $fields);
    }

    public function down()
    {
        // Revert date_of_commencement field to NOT NULL (if needed for rollback)
        $fields = [
            'date_of_commencement' => [
                'type' => 'DATE',
                'null' => false,
                'default' => '0000-00-00'
            ]
        ];

        $this->forge->modifyColumn('employee_personal_information', $fields);
    }
}
