<?php

namespace App\Models;

use CodeIgniter\Model;

class EmployeeProfessionalMembershipModel extends Model
{
    protected $table = 'employee_professional_membership';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'employee_id',
        'professional_affiliation',
        'license_number',
        'current_status',
        'renewal_date',
        'document_path',
        'is_verified'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'employee_id' => 'required|integer',
        'professional_affiliation' => 'permit_empty|max_length[255]',
        'license_number' => 'permit_empty|max_length[100]',
        'current_status' => 'permit_empty|in_list[active,expired,suspended,pending]',
        'renewal_date' => 'permit_empty|valid_date',
        'is_verified' => 'permit_empty|in_list[0,1]',
        'document_path' => 'permit_empty|max_length[500]'
    ];

    protected $validationMessages = [
        'renewal_date' => [
            'valid_date' => 'Please enter a valid renewal date.'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Get professional memberships by employee ID
     */
    public function getMembershipsByEmployee(int $employeeId): array
    {
        return $this->where('employee_id', $employeeId)
                   ->orderBy('current_status', 'ASC')
                   ->orderBy('renewal_date', 'ASC')
                   ->findAll();
    }

    /**
     * Get single membership by ID and employee ID
     */
    public function getMembershipByEmployeeAndId(int $employeeId, int $membershipId): ?array
    {
        return $this->where(['employee_id' => $employeeId, 'id' => $membershipId])->first();
    }

    /**
     * Get active memberships for employee
     */
    public function getActiveMemberships(int $employeeId): array
    {
        return $this->where(['employee_id' => $employeeId, 'current_status' => 'active'])
                   ->orderBy('renewal_date', 'ASC')
                   ->findAll();
    }

    /**
     * Get expired memberships for employee
     */
    public function getExpiredMemberships(int $employeeId): array
    {
        return $this->where(['employee_id' => $employeeId, 'current_status' => 'expired'])
                   ->orderBy('renewal_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get memberships expiring soon
     */
    public function getMembershipsExpiringSoon(int $employeeId, int $days = 30): array
    {
        $futureDate = date('Y-m-d', strtotime("+{$days} days"));
        
        return $this->where('employee_id', $employeeId)
                   ->where('current_status', 'active')
                   ->where('renewal_date <=', $futureDate)
                   ->where('renewal_date >=', date('Y-m-d'))
                   ->orderBy('renewal_date', 'ASC')
                   ->findAll();
    }

    /**
     * Get membership statistics for employee
     */
    public function getMembershipStats(int $employeeId): array
    {
        $total = $this->where('employee_id', $employeeId)->countAllResults();
        $active = $this->where(['employee_id' => $employeeId, 'current_status' => 'active'])->countAllResults();
        $expired = $this->where(['employee_id' => $employeeId, 'current_status' => 'expired'])->countAllResults();
        $suspended = $this->where(['employee_id' => $employeeId, 'current_status' => 'suspended'])->countAllResults();
        $verified = $this->where(['employee_id' => $employeeId, 'is_verified' => 1])->countAllResults();
        $expiringSoon = count($this->getMembershipsExpiringSoon($employeeId, 30));
        
        return [
            'total_memberships' => $total,
            'active_memberships' => $active,
            'expired_memberships' => $expired,
            'suspended_memberships' => $suspended,
            'verified_memberships' => $verified,
            'expiring_soon' => $expiringSoon,
            'verification_percentage' => $total > 0 ? round(($verified / $total) * 100, 1) : 0
        ];
    }

    /**
     * Update membership status
     */
    public function updateMembershipStatus(int $membershipId, string $status): bool
    {
        if (!in_array($status, ['active', 'expired', 'suspended'])) {
            return false;
        }
        
        return $this->update($membershipId, ['current_status' => $status]);
    }

    /**
     * Update verification status
     */
    public function updateVerificationStatus(int $membershipId, bool $isVerified): bool
    {
        return $this->update($membershipId, ['is_verified' => $isVerified ? 1 : 0]);
    }

    /**
     * Delete membership record with document cleanup
     */
    public function deleteMembershipRecord(int $membershipId): bool
    {
        $membership = $this->find($membershipId);
        
        if ($membership && !empty($membership['document_path'])) {
            $filePath = ROOTPATH . $membership['document_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        
        return $this->delete($membershipId);
    }

    /**
     * Get memberships with employee details for agency
     */
    public function getMembershipsWithEmployeeByAgency(int $agencyId): array
    {
        return $this->select('employee_professional_membership.*, employee_personal_information.first_name, employee_personal_information.last_name, employee_personal_information.employment_number')
                   ->join('employee_personal_information', 'employee_personal_information.id = employee_professional_membership.employee_id')
                   ->where('employee_personal_information.agency_id', $agencyId)
                   ->orderBy('employee_professional_membership.current_status', 'ASC')
                   ->orderBy('employee_professional_membership.renewal_date', 'ASC')
                   ->findAll();
    }

    /**
     * Get agency-wide membership statistics
     */
    public function getAgencyMembershipStats(int $agencyId): array
    {
        $query = $this->select('employee_professional_membership.*')
                     ->join('employee_personal_information', 'employee_personal_information.id = employee_professional_membership.employee_id')
                     ->where('employee_personal_information.agency_id', $agencyId);
        
        $total = $query->countAllResults(false);
        $active = $query->where('employee_professional_membership.current_status', 'active')->countAllResults(false);
        $expired = $query->where('employee_professional_membership.current_status', 'expired')->countAllResults(false);
        $expiringSoon = $query->where('employee_professional_membership.current_status', 'active')
                             ->where('employee_professional_membership.renewal_date <=', date('Y-m-d', strtotime('+30 days')))
                             ->where('employee_professional_membership.renewal_date >=', date('Y-m-d'))
                             ->countAllResults();
        
        return [
            'total_memberships' => $total,
            'active_memberships' => $active,
            'expired_memberships' => $expired,
            'expiring_soon' => $expiringSoon
        ];
    }
}
