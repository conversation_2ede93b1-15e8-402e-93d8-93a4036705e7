<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<!-- Navigation Buttons -->
<div class="row mb-4">
    <div class="col-6">
        <a href="<?= base_url('agency/onboarding') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Employee List
        </a>
    </div>
    <div class="col-6 text-end">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/profile') ?>" class="btn btn-success">
            <i class="bi bi-check-circle"></i> Complete Onboarding
        </a>
    </div>
</div>

<!-- Onboarding Steps Navigation -->
<div class="row mb-4">
    <div class="col-md-3 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/profile') ?>" class="btn btn-outline-primary w-100">
            <i class="bi bi-person"></i><br>
            <small>Personal Info</small>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/education') ?>" class="btn btn-outline-info w-100">
            <i class="bi bi-mortarboard"></i><br>
            <small>Education</small>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/memberships') ?>" class="btn btn-outline-success w-100">
            <i class="bi bi-award"></i><br>
            <small>Memberships</small>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/files') ?>" class="btn btn-outline-warning w-100">
            <i class="bi bi-file-earmark"></i><br>
            <small>Files</small>
        </a>
    </div>
    <div class="col-md-3 mb-2">
        <button class="btn btn-warning w-100" disabled>
            <i class="bi bi-briefcase"></i><br>
            <small>Employment</small>
        </button>
    </div>
</div>

<!-- Page Header -->
<div class="text-center mb-4">
    <h2 class="mb-1">
        <i class="bi bi-person-circle text-primary"></i>
        <?= esc($employee['first_name'] . ' ' . $employee['last_name']) ?>
    </h2>
    <p class="text-muted mb-0">
        <?php if (!empty($employee['employment_number'])): ?>
            File Number: <?= esc($employee['employment_number']) ?> •
        <?php endif; ?>
        Employment History
    </p>
</div>

<!-- Employment Stats Row -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary"><?= $stats['total_employments'] ?></h4>
                <p class="text-muted mb-0">Total Jobs</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info"><?= $stats['total_experience_years'] ?></h4>
                <p class="text-muted mb-0">Years Experience</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <?php if (!empty($current_employment)): ?>
                    <h4 class="text-success">Current</h4>
                    <p class="text-muted mb-0"><?= esc(substr($current_employment['designation'], 0, 15)) ?><?= strlen($current_employment['designation']) > 15 ? '...' : '' ?></p>
                <?php else: ?>
                    <h4 class="text-muted">-</h4>
                    <p class="text-muted mb-0">No Current Job</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning"><?= count($employment_history) ?></h4>
                <p class="text-muted mb-0">Total Records</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Employment Content -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-briefcase text-warning"></i>
            Employment History
        </h5>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmploymentModal">
                <i class="bi bi-plus-circle"></i> Add Employment
            </button>
        </div>
    </div>
            <div class="card-body">
                <?php if (empty($employment_history)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-briefcase text-muted" style="font-size: 4rem;"></i>
                        <h5 class="text-muted mt-3">No employment history found</h5>
                        <p class="text-muted">Start by adding the employee's employment history.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmploymentModal">
                            <i class="bi bi-plus-circle"></i> Add First Employment Record
                        </button>
                    </div>
                <?php else: ?>
                    <!-- Employment Timeline -->
                    <div class="timeline">
                        <?php foreach ($employment_history as $index => $employment): ?>
                            <?php
                            $startDate = new DateTime($employment['start_date']);
                            $endDate = $employment['end_date'] ? new DateTime($employment['end_date']) : new DateTime();
                            $duration = $startDate->diff($endDate);
                            $durationText = '';
                            
                            if ($duration->y > 0) {
                                $durationText .= $duration->y . ' year' . ($duration->y > 1 ? 's' : '');
                            }
                            if ($duration->m > 0) {
                                if ($durationText) $durationText .= ', ';
                                $durationText .= $duration->m . ' month' . ($duration->m > 1 ? 's' : '');
                            }
                            if (!$durationText) {
                                $durationText = 'Less than a month';
                            }
                            ?>
                            
                            <div class="timeline-item <?= $employment['is_current'] ? 'current' : '' ?>">
                                <div class="timeline-marker">
                                    <?php if ($employment['is_current']): ?>
                                        <i class="bi bi-circle-fill text-success"></i>
                                    <?php else: ?>
                                        <i class="bi bi-circle text-muted"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="timeline-content">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <h6 class="card-title mb-1">
                                                        <?= esc($employment['designation']) ?>
                                                        <?php if ($employment['is_current']): ?>
                                                            <span class="badge bg-success ms-2">Current</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="card-text text-muted mb-2">
                                                        <i class="bi bi-building"></i> <?= esc($employment['employer_name']) ?>
                                                    </p>
                                                    <p class="card-text mb-2">
                                                        <i class="bi bi-calendar-range"></i> 
                                                        <small>
                                                            <?= $startDate->format('M Y') ?> - 
                                                            <?= $employment['is_current'] ? 'Present' : $endDate->format('M Y') ?>
                                                            <span class="text-muted">(<?= $durationText ?>)</span>
                                                        </small>
                                                    </p>
                                                    <small class="text-muted">
                                                        <i class="bi bi-clock"></i> 
                                                        Added: <?= date('M d, Y', strtotime($employment['created_at'])) ?>
                                                    </small>
                                                </div>
                                                <div class="d-flex flex-column gap-2">
                                                    <button class="btn btn-sm btn-outline-primary" type="button" 
                                                            onclick="editEmployment(<?= $employment['id'] ?>)"
                                                            title="Edit Employment">
                                                        <i class="bi bi-pencil"></i> Edit
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" type="button" 
                                                            onclick="deleteEmployment(<?= $employment['id'] ?>)"
                                                            title="Delete Employment">
                                                        <i class="bi bi-trash"></i> Delete
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Employment Modal -->
<div class="modal fade" id="addEmploymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Employment Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="<?= base_url('agency/onboarding/' . $employee['id'] . '/employment-history/store') ?>">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="employer_name" class="form-label">Employer Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="employer_name" name="employer_name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="designation" class="form-label">Designation <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="designation" name="designation" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                                <div class="form-text">Leave blank if this is current employment</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_current" name="is_current" value="1">
                        <label class="form-check-label" for="is_current">
                            This is my current employment
                        </label>
                        <div class="form-text">Check this if the employee is currently working at this position</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Employment Record</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Employment Modal -->
<div class="modal fade" id="editEmploymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Employment Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editEmploymentForm" method="POST">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <!-- Form fields will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Employment Record</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteEmploymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this employment record? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteEmploymentForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 30px;
    bottom: -30px;
    width: 2px;
    background-color: #dee2e6;
}

.timeline-item.current:not(:last-child)::before {
    background-color: #198754;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    z-index: 1;
}

.timeline-content {
    margin-left: 20px;
}
</style>

<script>
// Employment records data for JavaScript
const employmentRecords = <?= json_encode($employment_history) ?>;

// Handle current employment checkbox
document.getElementById('is_current').addEventListener('change', function() {
    const endDateField = document.getElementById('end_date');
    if (this.checked) {
        endDateField.value = '';
        endDateField.disabled = true;
    } else {
        endDateField.disabled = false;
    }
});

function editEmployment(employmentId) {
    const employment = employmentRecords.find(e => e.id == employmentId);
    if (!employment) return;
    
    const form = document.getElementById('editEmploymentForm');
    form.action = `<?= base_url('agency/onboarding/' . $employee['id'] . '/employment-history/') ?>${employmentId}/update`;
    
    // Populate form fields
    const modalBody = form.querySelector('.modal-body');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label for="edit_employer_name" class="form-label">Employer Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_employer_name" name="employer_name" 
                           value="${employment.employer_name}" required>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="edit_designation" class="form-label">Designation <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_designation" name="designation" 
                           value="${employment.designation}" required>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="edit_start_date" name="start_date" 
                           value="${employment.start_date}" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="edit_end_date" name="end_date" 
                           value="${employment.end_date || ''}" ${employment.is_current ? 'disabled' : ''}>
                </div>
            </div>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="edit_is_current" name="is_current" value="1" 
                   ${employment.is_current ? 'checked' : ''}>
            <label class="form-check-label" for="edit_is_current">
                This is my current employment
            </label>
        </div>
    `;
    
    // Add event listener for current employment checkbox
    const currentCheckbox = modalBody.querySelector('#edit_is_current');
    const endDateField = modalBody.querySelector('#edit_end_date');
    
    currentCheckbox.addEventListener('change', function() {
        if (this.checked) {
            endDateField.value = '';
            endDateField.disabled = true;
        } else {
            endDateField.disabled = false;
        }
    });
    
    new bootstrap.Modal(document.getElementById('editEmploymentModal')).show();
}

function deleteEmployment(employmentId) {
    const form = document.getElementById('deleteEmploymentForm');
    form.action = `<?= base_url('agency/onboarding/' . $employee['id'] . '/employment-history/') ?>${employmentId}/delete`;
    
    new bootstrap.Modal(document.getElementById('deleteEmploymentModal')).show();
}
</script>

<?= $this->endSection() ?>
