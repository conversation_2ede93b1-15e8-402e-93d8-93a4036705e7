<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Create User</h1>
        <p class="text-muted mb-0">Add a new user to the system</p>
    </div>
    <div>
        <a href="<?= base_url('/admin/users') ?>" class="btn btn-outline-secondary">
            <i class="material-icons me-2">arrow_back</i>
            Back to List
        </a>
    </div>
</div>

<!-- Create Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons me-2">person_add</i>
                    User Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= base_url('/admin/users/store') ?>">
                    <?= csrf_field() ?>
                    
                    <!-- Personal Information -->
                    <div class="row">
                        <!-- First Name -->
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">
                                First Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('first_name')) ? 'is-invalid' : '' ?>" 
                                   id="first_name" 
                                   name="first_name" 
                                   value="<?= old('first_name') ?>" 
                                   required>
                            <?php if (isset($validation) && $validation->hasError('first_name')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('first_name') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Last Name -->
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">
                                Last Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('last_name')) ? 'is-invalid' : '' ?>" 
                                   id="last_name" 
                                   name="last_name" 
                                   value="<?= old('last_name') ?>" 
                                   required>
                            <?php if (isset($validation) && $validation->hasError('last_name')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('last_name') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Username -->
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">
                                Username <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('username')) ? 'is-invalid' : '' ?>" 
                                   id="username" 
                                   name="username" 
                                   value="<?= old('username') ?>" 
                                   required>
                            <div class="form-text">Username must be unique and at least 3 characters long</div>
                            <?php if (isset($validation) && $validation->hasError('username')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('username') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                Email Address <span class="text-danger">*</span>
                            </label>
                            <input type="email" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('email')) ? 'is-invalid' : '' ?>" 
                                   id="email" 
                                   name="email" 
                                   value="<?= old('email') ?>" 
                                   required>
                            <?php if (isset($validation) && $validation->hasError('email')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('email') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Phone Number -->
                    <div class="mb-3">
                        <label for="phone_number" class="form-label">Phone Number</label>
                        <input type="tel" 
                               class="form-control <?= (isset($validation) && $validation->hasError('phone_number')) ? 'is-invalid' : '' ?>" 
                               id="phone_number" 
                               name="phone_number" 
                               value="<?= old('phone_number') ?>">
                        <div class="form-text">Optional - Include country code if international</div>
                        <?php if (isset($validation) && $validation->hasError('phone_number')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('phone_number') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <hr class="my-4">

                    <!-- Account Settings -->
                    <h6 class="mb-3">Account Settings</h6>
                    
                    <div class="row">
                        <!-- Password -->
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                Password <span class="text-danger">*</span>
                            </label>
                            <input type="password" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('password')) ? 'is-invalid' : '' ?>" 
                                   id="password" 
                                   name="password" 
                                   required>
                            <div class="form-text">Minimum 4 characters</div>
                            <?php if (isset($validation) && $validation->hasError('password')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('password') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Confirm Password -->
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">
                                Confirm Password <span class="text-danger">*</span>
                            </label>
                            <input type="password" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('confirm_password')) ? 'is-invalid' : '' ?>" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required>
                            <?php if (isset($validation) && $validation->hasError('confirm_password')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('confirm_password') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Role -->
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">
                                Role <span class="text-danger">*</span>
                            </label>
                            <select class="form-select <?= (isset($validation) && $validation->hasError('role')) ? 'is-invalid' : '' ?>" 
                                    id="role" 
                                    name="role" 
                                    required>
                                <option value="">-- Select Role --</option>
                                <?php foreach ($roleOptions as $value => $label): ?>
                                    <option value="<?= esc($value) ?>" <?= old('role') === $value ? 'selected' : '' ?>>
                                        <?= esc($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Determines user permissions and access level</div>
                            <?php if (isset($validation) && $validation->hasError('role')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('role') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Agency Assignment (Only for User role) -->
                        <div class="col-md-6 mb-3" id="agency-assignment-field" style="display: none;">
                            <label for="agency_id" class="form-label">
                                Agency Assignment
                            </label>
                            <select class="form-select <?= (isset($validation) && $validation->hasError('agency_id')) ? 'is-invalid' : '' ?>"
                                    id="agency_id"
                                    name="agency_id">
                                <?php foreach ($agencyOptions as $value => $label): ?>
                                    <option value="<?= esc($value) ?>" <?= old('agency_id') === $value ? 'selected' : '' ?>>
                                        <?= esc($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Optional: Assign user to an agency for Agency Portal access. Leave blank for Admin Portal only.</div>
                            <?php if (isset($validation) && $validation->hasError('agency_id')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('agency_id') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-3">
                        <label for="is_active" class="form-label">Status</label>
                        <select class="form-select <?= (isset($validation) && $validation->hasError('is_active')) ? 'is-invalid' : '' ?>" 
                                id="is_active" 
                                name="is_active">
                            <?php foreach ($statusOptions as $value => $label): ?>
                                <option value="<?= esc($value) ?>" <?= old('is_active', '1') === $value ? 'selected' : '' ?>>
                                    <?= esc($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($validation) && $validation->hasError('is_active')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('is_active') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="material-icons me-2">save</i>
                            Create User
                        </button>
                        <a href="<?= base_url('/admin/users') ?>" class="btn btn-outline-secondary">
                            <i class="material-icons me-2">cancel</i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Help Card -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="material-icons me-2">help</i>
                    User Creation Guide
                </h6>
            </div>
            <div class="card-body">
                <h6>Role Descriptions:</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge bg-danger me-2">Admin</span>
                        Full system access and user management
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-dark me-2">CEO</span>
                        Executive oversight and strategic management
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-warning me-2">Manager</span>
                        Department oversight and reporting
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-info me-2">User</span>
                        Basic access to assigned features
                    </li>
                </ul>

                <hr>

                <h6>Agency Assignment:</h6>
                <p class="small text-muted">
                    • <strong>No Agency:</strong> Admin Portal user with system-wide access<br>
                    • <strong>With Agency:</strong> Agency Portal user with organization-specific access
                </p>

                <hr>

                <h6>Password Requirements:</h6>
                <ul class="small text-muted">
                    <li>Minimum 4 characters</li>
                    <li>User will be able to change password after first login</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role');
    const agencyField = document.getElementById('agency-assignment-field');
    const agencySelect = document.getElementById('agency_id');

    function toggleAgencyField() {
        const selectedRole = roleSelect.value;

        if (selectedRole === 'user') {
            agencyField.style.display = 'block';
        } else {
            agencyField.style.display = 'none';
            agencySelect.value = ''; // Clear selection when hidden
        }
    }

    // Initial check
    toggleAgencyField();

    // Listen for role changes
    roleSelect.addEventListener('change', toggleAgencyField);
});
</script>
