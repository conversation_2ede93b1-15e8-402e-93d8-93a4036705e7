<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<!-- Navigation Buttons -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="<?= base_url('agency/onboarding') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Employee List
        </a>
    </div>
    <div>
        <a href="<?= base_url('agency/dashboard') ?>" class="btn btn-outline-primary">
            <i class="bi bi-house-door"></i> Dashboard
        </a>
    </div>
</div>

<!-- Page Header -->
<div class="text-center mb-4">
    <h2 class="mb-1">
        <i class="bi bi-person-plus-fill text-primary"></i> Create New Employee
    </h2>
    <p class="text-muted mb-0">Enter basic employee information to start the onboarding process</p>
</div>

<!-- Full Width Form -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-info-circle text-primary"></i> Basic Information
        </h5>
    </div>
            <div class="card-body">
                <form method="POST" action="<?= base_url('agency/onboarding/store') ?>">
                    <?= csrf_field() ?>
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?= old('first_name') ?>" required>
                                <div class="invalid-feedback">
                                    Please provide a valid first name.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="middle_name" class="form-label">Middle Name</label>
                                <input type="text" class="form-control" id="middle_name" name="middle_name" 
                                       value="<?= old('middle_name') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?= old('last_name') ?>" required>
                                <div class="invalid-feedback">
                                    Please provide a valid last name.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="email_address" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email_address" name="email_address"
                                       value="<?= old('email_address') ?>">
                                <div class="form-text">
                                    Optional. If provided, this email will be used for employee communication and public profile access.
                                </div>
                                <div class="invalid-feedback">
                                    Please provide a valid email address format.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="male" <?= old('gender') === 'male' ? 'selected' : '' ?>>Male</option>
                                    <option value="female" <?= old('gender') === 'female' ? 'selected' : '' ?>>Female</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a gender.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Public Profile Settings -->
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="bi bi-link-45deg text-accent"></i>
                                Public Profile Access
                            </h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="public_profile_enabled" 
                                       name="public_profile_enabled" value="1" 
                                       <?= old('public_profile_enabled') ? 'checked' : 'checked' ?>>
                                <label class="form-check-label" for="public_profile_enabled">
                                    Enable employee public profile access
                                </label>
                            </div>
                            <small class="text-muted">
                                When enabled, a secure link will be generated allowing the employee to complete their profile information online.
                                An email address is helpful for sharing this link but not required.
                            </small>
                        </div>
                    </div>

                    <!-- Information Notice -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle"></i>
                            What happens next?
                        </h6>
                        <ul class="mb-0">
                            <li>Employee record will be created with basic information</li>
                            <li>You'll be redirected to complete the employee profile</li>
                            <li>Employment number will be assigned manually during the profile completion</li>
                            <li>If public access is enabled, a secure link will be created for the employee</li>
                            <li>The employee can then complete their information and upload documents</li>
                        </ul>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Create Employee
                        </button>
                    </div>
                </form>
            </div>
        </div>

<!-- Quick Actions Card -->
<div class="card border-0 bg-light mt-4">
    <div class="card-body text-center">
        <h6 class="text-muted">Need to manage existing employees?</h6>
        <div class="btn-group">
            <a href="<?= base_url('agency/onboarding') ?>" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-list"></i> View All Employees
            </a>
            <a href="<?= base_url('agency/reports') ?>" class="btn btn-outline-info btn-sm">
                <i class="bi bi-graph-up"></i> View Reports
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input[required], select[required]');
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        inputs.forEach(function(input) {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        });
        
        // Email validation
        const emailInput = document.getElementById('email_address');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailInput.value && !emailRegex.test(emailInput.value)) {
            emailInput.classList.add('is-invalid');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            e.stopPropagation();
        }
    });
    
    // Real-time validation
    inputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            if (this.value.trim()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
});
</script>

<?= $this->endSection() ?>
