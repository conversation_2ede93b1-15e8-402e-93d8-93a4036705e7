#--------------------------------------------------------------------
# Alternative Database Configuration for <PERSON>ealth W<PERSON>man
# Use this if the main .env doesn't work
#--------------------------------------------------------------------

# ENVIRONMENT
CI_ENVIRONMENT = development

# APP
app.baseURL = 'http://localhost/chealthwokman/'

# DATABASE - Option 1: Using 127.0.0.1 with root
database.default.hostname = 127.0.0.1
database.default.database = chealthwokman_db
database.default.username = root
database.default.password =
database.default.DBDriver = MySQLi
database.default.DBPrefix =
database.default.port = 3306

# DATABASE - Option 2: Using dedicated user (uncomment if you create the user)
# database.default.hostname = localhost
# database.default.database = chealthwokman_db
# database.default.username = chealthwokman
# database.default.password = chealthwokman123
# database.default.DBDriver = MySQLi
# database.default.DBPrefix =
# database.default.port = 3306

# DATABASE - Option 3: Using socket connection (for some XAMPP setups)
# database.default.hostname = localhost
# database.default.database = chealthwokman_db
# database.default.username = root
# database.default.password =
# database.default.DBDriver = MySQLi
# database.default.DBPrefix =
# database.default.port = 3306
# database.default.socket = /tmp/mysql.sock
