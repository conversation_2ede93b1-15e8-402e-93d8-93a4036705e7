<?php

namespace App\Models;

use CodeIgniter\Model;

class EmployeeFilesModel extends Model
{
    protected $table = 'employee_files';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'employee_id',
        'file_title',
        'file_description',
        'file_path',
        'is_verified'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'employee_id' => 'required|integer',
        'file_title' => 'required|min_length[2]|max_length[255]',
        'file_description' => 'permit_empty',
        'file_path' => 'required|max_length[500]',
        'is_verified' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'file_title' => [
            'required' => 'File title is required.',
            'min_length' => 'File title must be at least 2 characters long.',
            'max_length' => 'File title cannot exceed 255 characters.'
        ],
        'file_path' => [
            'required' => 'File path is required.',
            'max_length' => 'File path cannot exceed 500 characters.'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Get files by employee ID
     */
    public function getFilesByEmployee(int $employeeId): array
    {
        return $this->where('employee_id', $employeeId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get file by employee and file ID
     */
    public function getFileByEmployeeAndId(int $employeeId, int $fileId): ?array
    {
        return $this->where(['employee_id' => $employeeId, 'id' => $fileId])->first();
    }

    /**
     * Get files statistics for an employee
     */
    public function getFilesStats(int $employeeId): array
    {
        $files = $this->where('employee_id', $employeeId)->findAll();
        
        $stats = [
            'total_files' => count($files),
            'verified_files' => 0,
            'unverified_files' => 0
        ];

        foreach ($files as $file) {
            if ($file['is_verified']) {
                $stats['verified_files']++;
            } else {
                $stats['unverified_files']++;
            }
        }

        return $stats;
    }

    /**
     * Get verified files for an employee
     */
    public function getVerifiedFiles(int $employeeId): array
    {
        return $this->where(['employee_id' => $employeeId, 'is_verified' => 1])
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get unverified files for an employee
     */
    public function getUnverifiedFiles(int $employeeId): array
    {
        return $this->where(['employee_id' => $employeeId, 'is_verified' => 0])
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Update verification status
     */
    public function updateVerificationStatus(int $fileId, bool $isVerified): bool
    {
        return $this->update($fileId, ['is_verified' => $isVerified ? 1 : 0]);
    }

    /**
     * Delete file record with document cleanup
     */
    public function deleteFileRecord(int $fileId): bool
    {
        $file = $this->find($fileId);
        
        if ($file && !empty($file['file_path'])) {
            $filePath = ROOTPATH . $file['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        
        return $this->delete($fileId);
    }

    /**
     * Get total file count for an employee
     */
    public function getTotalFileCount(int $employeeId): int
    {
        return $this->where('employee_id', $employeeId)->countAllResults();
    }

    /**
     * Check if employee has any files
     */
    public function hasFiles(int $employeeId): bool
    {
        return $this->where('employee_id', $employeeId)->countAllResults() > 0;
    }
}
