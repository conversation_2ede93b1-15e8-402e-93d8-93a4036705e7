<?php

// Test database connection for <PERSON>ealth Wokman
require_once 'vendor/autoload.php';

// Bootstrap CodeIgniter to load environment variables
$app = \Config\Services::codeigniter();

echo "=== CHealth Wokman Database Connection Test ===" . PHP_EOL;
echo PHP_EOL;

// Test environment variables
echo "Environment Variables from .env:" . PHP_EOL;
echo "hostname: " . env('database.default.hostname', 'NOT SET') . PHP_EOL;
echo "database: " . env('database.default.database', 'NOT SET') . PHP_EOL;
echo "username: " . env('database.default.username', 'NOT SET') . PHP_EOL;
echo "port: " . env('database.default.port', 'NOT SET') . PHP_EOL;
echo PHP_EOL;

// Test database configuration
try {
    $config = new \Config\Database();
    $dbConfig = $config->default;
    
    echo "Database Configuration:" . PHP_EOL;
    echo "hostname: " . $dbConfig['hostname'] . PHP_EOL;
    echo "database: " . $dbConfig['database'] . PHP_EOL;
    echo "username: " . $dbConfig['username'] . PHP_EOL;
    echo "port: " . $dbConfig['port'] . PHP_EOL;
    echo PHP_EOL;
    
    // Test connection using PDO
    echo "Testing connection with PDO..." . PHP_EOL;
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    echo "✓ SUCCESS: Database server connection working!" . PHP_EOL;
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$dbConfig['database']}'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Database '{$dbConfig['database']}' exists." . PHP_EOL;
    } else {
        echo "⚠ Database '{$dbConfig['database']}' does not exist." . PHP_EOL;
        echo "Creating database..." . PHP_EOL;
        $pdo->exec("CREATE DATABASE {$dbConfig['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✓ Database created successfully!" . PHP_EOL;
    }
    
    // Test connection to the specific database
    $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    echo "✓ SUCCESS: Connection to database '{$dbConfig['database']}' working!" . PHP_EOL;
    
} catch (Exception $e) {
    echo "✗ ERROR: " . $e->getMessage() . PHP_EOL;
    echo PHP_EOL;
    echo "Troubleshooting suggestions:" . PHP_EOL;
    echo "1. Make sure XAMPP MySQL/MariaDB is running" . PHP_EOL;
    echo "2. Try accessing phpMyAdmin at http://localhost/phpmyadmin" . PHP_EOL;
    echo "3. Check if the .env file has correct database settings" . PHP_EOL;
}

echo PHP_EOL;
echo "Test completed." . PHP_EOL;
