<?php

namespace App\Controllers\Agency;

class AgencyReportsController extends AgencyBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Display reports dashboard
     */
    public function index()
    {
        $agencyId = $this->getCurrentAgencyId();
        $agency = $this->getCurrentAgency();
        
        if (!$agencyId) {
            $this->setFlashMessage('error', 'No agency assignment found. Please contact administrator.');
            return redirect()->to('/admin/dashboard');
        }

        $data = [
            'pageTitle' => 'Reports Dashboard',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => '/agency/dashboard'],
                ['title' => 'Reports', 'url' => '']
            ],
            'agencyId' => $agencyId,
            'agency' => $agency,
            'systemStats' => [
                'reports_available' => 3,
                'last_updated' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ]
        ];

        return view('agency/reports/reports_index', $data);
    }

    /**
     * Employee reports - Feature not available
     */
    public function employees()
    {
        $this->setFlashMessage('info', 'Employee reports feature is not available in this version.');
        return redirect()->to('/agency/reports');
    }

    /**
     * Onboarding completion reports - Feature not available
     */
    public function onboarding()
    {
        $this->setFlashMessage('info', 'Onboarding reports feature is not available in this version.');
        return redirect()->to('/agency/reports');
    }

    /**
     * Document reports - Feature not available
     */
    public function documents()
    {
        $this->setFlashMessage('info', 'Document reports feature is not available in this version.');
        return redirect()->to('/agency/reports');
    }

    /**
     * Banking reports - Feature not available
     */
    public function banking()
    {
        $this->setFlashMessage('info', 'Banking reports feature is not available in this version.');
        return redirect()->to('/agency/reports');
    }
}
