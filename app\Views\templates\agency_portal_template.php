<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' : '' ?>Agency Portal - CHS PNG</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            /* CHS Official Color Scheme */
            --chs-blue: #1A4E8C;           /* Deep Cerulean Blue - Primary brand color */
            --chs-red: #C8102E;            /* Engine Red - Cross symbol color */
            --chs-black: #000000;          /* Black - CHS/PNG text */
            --chs-white: #FFFFFF;          /* White - Logo background */

            /* CHS Color Variations for UI States */
            --chs-blue-light: #2A5E9C;     /* Lighter blue for secondary elements */
            --chs-blue-dark: #0A3E7C;      /* Darker blue for emphasis */
            --chs-red-light: #D8203E;      /* Lighter red for warnings */
            --chs-gray-light: #F8F9FA;     /* Light gray for backgrounds */
            --chs-gray-medium: #6C757D;    /* Medium gray for text */
            --chs-gray-dark: #343A40;      /* Dark gray for text */

            /* Agency Theme Variations */
            --agency-primary: var(--chs-blue);
            --agency-secondary: var(--chs-gray-light);
            --agency-accent: var(--chs-blue-light);
        }
        
        /* Bootstrap Color Overrides - CHS Compliant */
        .bg-primary {
            background-color: var(--chs-blue) !important;
        }

        .bg-warning {
            background-color: var(--chs-red-light) !important;
            color: var(--chs-white) !important;
        }

        .bg-info {
            background-color: var(--chs-blue-light) !important;
            color: var(--chs-white) !important;
        }

        .bg-success {
            background-color: var(--chs-blue-dark) !important;
            color: var(--chs-white) !important;
        }

        .bg-danger {
            background-color: var(--chs-red) !important;
        }

        .bg-secondary {
            background-color: var(--chs-gray-medium) !important;
        }

        /* Badge Color Overrides */
        .badge.bg-warning {
            background-color: var(--chs-red-light) !important;
            color: var(--chs-white) !important;
        }

        .badge.bg-info {
            background-color: var(--chs-blue-light) !important;
            color: var(--chs-white) !important;
        }

        .badge.bg-success {
            background-color: var(--chs-blue-dark) !important;
            color: var(--chs-white) !important;
        }

        .badge.bg-danger {
            background-color: var(--chs-red) !important;
            color: var(--chs-white) !important;
        }

        .badge.bg-secondary {
            background-color: var(--chs-gray-medium) !important;
            color: var(--chs-white) !important;
        }

        /* Button Color Overrides */
        .btn-outline-success {
            color: var(--chs-blue-dark);
            border-color: var(--chs-blue-dark);
        }

        .btn-outline-success:hover {
            background-color: var(--chs-blue-dark);
            border-color: var(--chs-blue-dark);
            color: var(--chs-white);
        }

        .btn-outline-warning {
            color: var(--chs-red-light);
            border-color: var(--chs-red-light);
        }

        .btn-outline-warning:hover {
            background-color: var(--chs-red-light);
            border-color: var(--chs-red-light);
            color: var(--chs-white);
        }

        .btn-outline-info {
            color: var(--chs-blue-light);
            border-color: var(--chs-blue-light);
        }

        .btn-outline-info:hover {
            background-color: var(--chs-blue-light);
            border-color: var(--chs-blue-light);
            color: var(--chs-white);
        }

        /* Text Color Overrides */
        .text-primary {
            color: var(--chs-blue) !important;
        }

        .text-success {
            color: var(--chs-blue-dark) !important;
        }

        .text-warning {
            color: var(--chs-red-light) !important;
        }

        .text-danger {
            color: var(--chs-red) !important;
        }

        .text-info {
            color: var(--chs-blue-light) !important;
        }

        .navbar-brand {
            font-weight: 600;
        }

        .agency-header {
            background: linear-gradient(135deg, var(--chs-blue) 0%, var(--chs-blue-dark) 100%);
            color: var(--chs-white);
            padding: 1rem 0;
            border-bottom: 3px solid var(--chs-red);
        }
        
        .nav-pills .nav-link {
            color: var(--dark-gray);
            border-radius: 0.5rem;
            margin: 0 0.25rem;
            font-weight: 500;
        }
        
        .nav-pills .nav-link.active {
            background-color: var(--chs-blue);
            color: var(--chs-white);
        }
        
        .nav-pills .nav-link:hover {
            background-color: var(--light-gray);
            color: var(--chs-blue);
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.75rem;
        }
        
        .card-header {
            background-color: var(--light-gray);
            border-bottom: 1px solid #dee2e6;
            border-radius: 0.75rem 0.75rem 0 0 !important;
        }
        
        .btn-primary {
            background-color: var(--chs-blue);
            border-color: var(--chs-blue);
            font-weight: 500;
        }
        
        .btn-primary:hover {
            background-color: #164080;
            border-color: #164080;
        }
        
        .btn-secondary {
            background-color: var(--medium-gray);
            border-color: var(--medium-gray);
            color: var(--chs-white);
        }
        
        .btn-danger {
            background-color: var(--chs-red);
            border-color: var(--chs-red);
        }
        
        .btn-danger:hover {
            background-color: #A60D26;
            border-color: #A60D26;
        }
        
        .btn-accent {
            background-color: var(--agency-accent);
            border-color: var(--agency-accent);
            color: var(--chs-white);
        }
        
        .btn-accent:hover {
            background-color: #0B5ED7;
            border-color: #0B5ED7;
            color: var(--chs-white);
        }
        
        .text-primary {
            color: var(--chs-blue) !important;
        }
        
        .text-danger {
            color: var(--chs-red) !important;
        }
        
        .text-accent {
            color: var(--agency-accent) !important;
        }
        
        .bg-primary {
            background-color: var(--chs-blue) !important;
        }
        
        .bg-accent {
            background-color: var(--agency-accent) !important;
        }
        
        .alert {
            border-radius: 0.75rem;
        }
        
        .breadcrumb {
            background-color: transparent;
            padding: 0;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: ">";
        }
        
        .stats-card {
            transition: transform 0.2s;
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* Ensure proper contrast for stats cards */
        .stats-card .card-title,
        .stats-card h3,
        .stats-card h4,
        .stats-card h6 {
            color: var(--chs-white) !important;
        }

        .stats-card .bi {
            opacity: 0.8;
        }
        
        .sidebar {
            min-height: calc(100vh - 200px);
        }
        
        .main-content {
            min-height: calc(100vh - 200px);
        }
        
        .footer {
            background-color: var(--chs-gray-dark);
            color: var(--chs-white);
            padding: 2rem 0;
            margin-top: 3rem;
            border-top: 3px solid var(--chs-blue);
        }
        
        /* DataTables Custom Styling */
        .dataTables_wrapper {
            font-family: inherit;
        }
        
        .dataTables_wrapper .dataTables_length select,
        .dataTables_wrapper .dataTables_filter input {
            border: 1px solid #DEE2E6;
            border-radius: 0.375rem;
            padding: 0.375rem 0.75rem;
        }
        
        .dataTables_wrapper .dataTables_filter input:focus {
            border-color: var(--chs-blue);
            box-shadow: 0 0 0 0.2rem rgba(26, 78, 140, 0.25);
        }
        
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: var(--chs-blue) !important;
            border-color: var(--chs-blue) !important;
            color: var(--chs-white) !important;
        }
        
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: var(--light-gray) !important;
            border-color: var(--chs-blue) !important;
            color: var(--chs-blue) !important;
        }
        
        .table-striped > tbody > tr:nth-of-type(odd) > td {
            background-color: rgba(26, 78, 140, 0.05);
        }
        
        .table thead th {
            background-color: var(--chs-blue);
            color: var(--chs-white);
            border-color: var(--chs-blue);
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background-color: rgba(26, 78, 140, 0.1);
        }
        
        /* Status Badges */
        .badge {
            font-weight: 500;
        }
        
        .badge.bg-success {
            background-color: var(--success-green) !important;
        }
        
        .badge.bg-warning {
            background-color: var(--warning-yellow) !important;
            color: var(--chs-black) !important;
        }
        
        .badge.bg-danger {
            background-color: var(--chs-red) !important;
        }
        
        /* Form Controls */
        .form-control:focus {
            border-color: var(--chs-blue);
            box-shadow: 0 0 0 0.2rem rgba(26, 78, 140, 0.25);
        }
        
        .form-select:focus {
            border-color: var(--chs-blue);
            box-shadow: 0 0 0 0.2rem rgba(26, 78, 140, 0.25);
        }
        
        /* Custom Agency Branding */
        .agency-logo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid var(--chs-white);
        }
        
        .agency-title {
            font-weight: 600;
            color: var(--chs-white);
        }
        
        .agency-subtitle {
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Agency Header -->
    <header class="agency-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8 d-flex align-items-center">
                    <img src="<?= base_url('assets/images/chs-logo.png') ?>" alt="CHS Logo"
                         class="agency-logo me-3">
                    <div>
                        <h4 class="mb-0 agency-title">
                            <?= isset($currentAgency) ? esc($currentAgency['name']) : 'Agency Portal' ?>
                        </h4>
                        <small class="agency-subtitle">
                            <?= isset($currentAgency) ? 'Code: ' . esc($currentAgency['agency_code']) : 'Christian Health Services PNG' ?>
                        </small>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <?= isset($currentUser) ? esc($currentUser['first_name'] . ' ' . $currentUser['last_name']) : 'User' ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= base_url('agency/profile') ?>"><i class="bi bi-person"></i> Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="<?= base_url('admin/logout') ?>" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile App Style - No Traditional Navigation -->

    <!-- Flash Messages -->
    <?php if (isset($flashMessages) && is_array($flashMessages)): ?>
        <div class="container-fluid mt-3">
            <?php foreach ($flashMessages as $type => $message): ?>
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?= $type === 'error' ? 'danger' : $type ?> alert-dismissible fade show" role="alert">
                        <?= esc($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- Breadcrumbs -->
    <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
        <div class="container-fluid mt-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <?php if (!empty($breadcrumb['url'])): ?>
                                    <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                                <?php else: ?>
                                    <?= esc($breadcrumb['title']) ?>
                                <?php endif; ?>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="container-fluid mt-4 main-content">
        <?= $this->renderSection('content') ?>
    </main>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <h6>CHS PNG - Agency Portal</h6>
                    <p class="mb-0 small">Employee Management System</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0 small">
                        &copy; <?= date('Y') ?> Church Health Services PNG. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery (required for DataTables) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // DataTables Global Configuration
        $(document).ready(function() {
            // Set DataTables defaults
            $.extend(true, $.fn.dataTable.defaults, {
                responsive: true,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                language: {
                    search: "Search records:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 to 0 of 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    },
                    emptyTable: "No data available in table",
                    zeroRecords: "No matching records found"
                },
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                buttons: [
                    {
                        extend: 'copy',
                        className: 'btn btn-sm btn-outline-secondary'
                    },
                    {
                        extend: 'excel',
                        className: 'btn btn-sm btn-outline-success',
                        title: 'CHS_Export_' + new Date().toISOString().slice(0, 10)
                    },
                    {
                        extend: 'pdf',
                        className: 'btn btn-sm btn-outline-danger',
                        title: 'CHS_Export_' + new Date().toISOString().slice(0, 10),
                        orientation: 'landscape'
                    },
                    {
                        extend: 'print',
                        className: 'btn btn-sm btn-outline-info'
                    }
                ]
            });
            
            // Initialize all tables with .data-table class
            $('.data-table').DataTable({
                buttons: true,
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12 col-md-6"B><"col-sm-12 col-md-6 text-end">>'+
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>'
            });
            
            // Initialize simple tables without export buttons
            $('.simple-table').DataTable({
                buttons: false
            });
            
            // Initialize readonly tables (no search, just pagination)
            $('.readonly-table').DataTable({
                searching: false,
                buttons: false,
                pageLength: 10,
                lengthChange: false
            });
        });
        
        // Custom DataTable utility functions
        window.CHSDataTables = {
            // Refresh table data
            refresh: function(tableId) {
                $(tableId).DataTable().ajax.reload();
            },
            
            // Clear all filters
            clearFilters: function(tableId) {
                $(tableId).DataTable().search('').columns().search('').draw();
            },
            
            // Export table to Excel
            exportToExcel: function(tableId, filename) {
                $(tableId).DataTable().button('.buttons-excel').trigger();
            },
            
            // Print table
            print: function(tableId) {
                $(tableId).DataTable().button('.buttons-print').trigger();
            }
        };
    </script>
    
    <?= $this->renderSection('scripts') ?>
</body>
</html>
