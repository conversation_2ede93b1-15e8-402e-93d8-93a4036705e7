<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdateEmployeeEducationTable extends Migration
{
    public function up()
    {
        // Add qualification_id field to reference education_qualifications table
        $fields = [
            'qualification_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'after' => 'employee_id'
            ]
        ];
        
        $this->forge->addColumn('employee_education', $fields);
        
        // Add foreign key constraint
        $this->forge->addForeignKey('qualification_id', 'education_qualifications', 'id', 'SET NULL', 'CASCADE', 'fk_employee_education_qualification');
        
        // Add index for better performance
        $this->db->query('ALTER TABLE employee_education ADD INDEX idx_qualification_id (qualification_id)');
    }

    public function down()
    {
        // Drop foreign key constraint
        $this->forge->dropForeignKey('employee_education', 'fk_employee_education_qualification');
        
        // Drop the column
        $this->forge->dropColumn('employee_education', 'qualification_id');
    }
}
