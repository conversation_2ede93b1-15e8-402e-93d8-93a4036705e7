<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddEmployeeIdPhotoAndExpiryDateFields extends Migration
{
    public function up()
    {
        $fields = [
            'employee_id_photo' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'public_profile_expires_at'
            ],
            'public_profile_expiry_date' => [
                'type' => 'DATE',
                'null' => true,
                'after' => 'public_profile_expires_at'
            ]
        ];
        
        $this->forge->addColumn('employee_personal_information', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('employee_personal_information', ['employee_id_photo', 'public_profile_expiry_date']);
    }
}