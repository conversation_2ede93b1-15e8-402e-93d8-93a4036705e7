<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Edit User</h1>
        <p class="text-muted mb-0">Update user information and permissions</p>
    </div>
    <div>
        <a href="<?= base_url('/admin/users/' . $adminUser['id']) ?>" class="btn btn-outline-info me-2">
            <i class="material-icons me-2">visibility</i>
            View User
        </a>
        <a href="<?= base_url('/admin/users') ?>" class="btn btn-outline-secondary">
            <i class="material-icons me-2">arrow_back</i>
            Back to List
        </a>
    </div>
</div>

<!-- Edit Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons me-2">edit</i>
                    Edit: <?= esc($adminUser['first_name'] . ' ' . $adminUser['last_name']) ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= base_url('/admin/users/' . $adminUser['id'] . '/update') ?>">
                    <?= csrf_field() ?>
                    
                    <!-- Personal Information -->
                    <div class="row">
                        <!-- First Name -->
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">
                                First Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('first_name')) ? 'is-invalid' : '' ?>" 
                                   id="first_name" 
                                   name="first_name" 
                                   value="<?= old('first_name', $adminUser['first_name']) ?>" 
                                   required>
                            <?php if (isset($validation) && $validation->hasError('first_name')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('first_name') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Last Name -->
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">
                                Last Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('last_name')) ? 'is-invalid' : '' ?>" 
                                   id="last_name" 
                                   name="last_name" 
                                   value="<?= old('last_name', $adminUser['last_name']) ?>" 
                                   required>
                            <?php if (isset($validation) && $validation->hasError('last_name')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('last_name') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Username -->
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">
                                Username <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('username')) ? 'is-invalid' : '' ?>" 
                                   id="username" 
                                   name="username" 
                                   value="<?= old('username', $adminUser['username']) ?>" 
                                   required>
                            <div class="form-text">Username must be unique and at least 3 characters long</div>
                            <?php if (isset($validation) && $validation->hasError('username')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('username') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                Email Address <span class="text-danger">*</span>
                            </label>
                            <input type="email" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('email')) ? 'is-invalid' : '' ?>" 
                                   id="email" 
                                   name="email" 
                                   value="<?= old('email', $adminUser['email']) ?>" 
                                   required>
                            <?php if (isset($validation) && $validation->hasError('email')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('email') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Phone Number -->
                    <div class="mb-3">
                        <label for="phone_number" class="form-label">Phone Number</label>
                        <input type="tel" 
                               class="form-control <?= (isset($validation) && $validation->hasError('phone_number')) ? 'is-invalid' : '' ?>" 
                               id="phone_number" 
                               name="phone_number" 
                               value="<?= old('phone_number', $adminUser['phone_number']) ?>">
                        <div class="form-text">Optional - Include country code if international</div>
                        <?php if (isset($validation) && $validation->hasError('phone_number')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('phone_number') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <hr class="my-4">

                    <!-- Password Change -->
                    <h6 class="mb-3">Change Password (Optional)</h6>
                    
                    <div class="row">
                        <!-- Password -->
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('password')) ? 'is-invalid' : '' ?>" 
                                   id="password" 
                                   name="password">
                            <div class="form-text">Leave blank to keep current password. Minimum 4 characters if changing.</div>
                            <?php if (isset($validation) && $validation->hasError('password')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('password') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Confirm Password -->
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('confirm_password')) ? 'is-invalid' : '' ?>" 
                                   id="confirm_password" 
                                   name="confirm_password">
                            <?php if (isset($validation) && $validation->hasError('confirm_password')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('confirm_password') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Account Settings -->
                    <h6 class="mb-3">Account Settings</h6>
                    
                    <div class="row">
                        <!-- Role -->
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">
                                Role <span class="text-danger">*</span>
                            </label>
                            <select class="form-select <?= (isset($validation) && $validation->hasError('role')) ? 'is-invalid' : '' ?>" 
                                    id="role" 
                                    name="role" 
                                    required>
                                <option value="">-- Select Role --</option>
                                <?php foreach ($roleOptions as $value => $label): ?>
                                    <option value="<?= esc($value) ?>" <?= old('role', $adminUser['role']) === $value ? 'selected' : '' ?>>
                                        <?= esc($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Determines user permissions and access level</div>
                            <?php if (isset($validation) && $validation->hasError('role')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('role') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Agency Assignment (Only for User role) -->
                        <div class="col-md-6 mb-3" id="agency-assignment-field" style="display: <?= old('role', $adminUser['role']) === 'user' ? 'block' : 'none' ?>;">
                            <label for="agency_id" class="form-label">
                                Agency Assignment
                            </label>
                            <select class="form-select <?= (isset($validation) && $validation->hasError('agency_id')) ? 'is-invalid' : '' ?>"
                                    id="agency_id"
                                    name="agency_id">
                                <?php foreach ($agencyOptions as $value => $label): ?>
                                    <option value="<?= esc($value) ?>" <?= old('agency_id', $adminUser['agency_id']) == $value ? 'selected' : '' ?>>
                                        <?= esc($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Optional: Assign user to an agency for Agency Portal access. Leave blank for Admin Portal only.</div>
                            <?php if (isset($validation) && $validation->hasError('agency_id')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('agency_id') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-3">
                        <label for="is_active" class="form-label">Status</label>
                        <select class="form-select <?= (isset($validation) && $validation->hasError('is_active')) ? 'is-invalid' : '' ?>" 
                                id="is_active" 
                                name="is_active">
                            <?php foreach ($statusOptions as $value => $label): ?>
                                <option value="<?= esc($value) ?>" <?= old('is_active', $adminUser['is_active']) == $value ? 'selected' : '' ?>>
                                    <?= esc($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($validation) && $validation->hasError('is_active')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('is_active') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="material-icons me-2">save</i>
                            Update User
                        </button>
                        <a href="<?= base_url('/admin/users/' . $adminUser['id']) ?>" class="btn btn-outline-secondary">
                            <i class="material-icons me-2">cancel</i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="material-icons me-2">info</i>
                    User Information
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="user-avatar mx-auto mb-2" style="width: 60px; height: 60px; font-size: 1.5rem;">
                        <?= strtoupper(substr($adminUser['first_name'], 0, 1) . substr($adminUser['last_name'], 0, 1)) ?>
                    </div>
                    <h6><?= esc($adminUser['first_name'] . ' ' . $adminUser['last_name']) ?></h6>
                    <small class="text-muted">@<?= esc($adminUser['username']) ?></small>
                </div>

                <hr>

                <div class="row text-sm">
                    <div class="col-6">
                        <strong>User ID:</strong><br>
                        <span class="text-muted">#<?= $adminUser['id'] ?></span>
                    </div>
                    <div class="col-6">
                        <strong>Created:</strong><br>
                        <span class="text-muted"><?= date('M j, Y', strtotime($adminUser['created_at'])) ?></span>
                    </div>
                </div>

                <hr>

                <h6>Current Settings:</h6>
                <ul class="list-unstyled small">
                    <li><strong>Role:</strong>
                        <span class="badge bg-<?= $adminUser['role'] === 'admin' ? 'danger' : ($adminUser['role'] === 'ceo' ? 'dark' : ($adminUser['role'] === 'manager' ? 'warning' : 'info')) ?>">
                            <?= ucfirst($adminUser['role']) ?>
                        </span>
                    </li>
                    <li><strong>Status:</strong> 
                        <span class="badge bg-<?= $adminUser['is_active'] ? 'success' : 'secondary' ?>">
                            <?= $adminUser['is_active'] ? 'Active' : 'Inactive' ?>
                        </span>
                    </li>
                    <li><strong>Portal:</strong> 
                        <?php if ($adminUser['agency_id']): ?>
                            <span class="badge bg-secondary">Agency User</span>
                        <?php else: ?>
                            <span class="badge bg-primary">Admin Portal</span>
                        <?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.user-avatar {
    background: linear-gradient(135deg, #1A4E8C 0%, #2563EB 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.text-sm {
    font-size: 0.875rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role');
    const agencyField = document.getElementById('agency-assignment-field');
    const agencySelect = document.getElementById('agency_id');

    function toggleAgencyField() {
        const selectedRole = roleSelect.value;

        if (selectedRole === 'user') {
            agencyField.style.display = 'block';
        } else {
            agencyField.style.display = 'none';
            // Don't clear selection when editing - preserve existing value
        }
    }

    // Initial check
    toggleAgencyField();

    // Listen for role changes
    roleSelect.addEventListener('change', function() {
        toggleAgencyField();

        // Clear agency selection only when changing away from user role
        if (roleSelect.value !== 'user') {
            agencySelect.value = '';
        }
    });
});
</script>
