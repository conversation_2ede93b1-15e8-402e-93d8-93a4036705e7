<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Libraries\AdminAuth;

class AgencyAuthFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $auth = new AdminAuth();
        $session = \Config\Services::session();

        // Check if user is logged in
        if (!$auth->check()) {
            // Store intended URL for redirect after login
            $session->set('admin_intended_url', current_url());
            
            // Set flash message
            $session->setFlashdata('flash_error', 'Please log in to access the Agency Portal.');
            
            // Redirect to login
            return redirect()->to('/admin/login');
        }

        // Validate session timeout
        if (!$auth->validateSession()) {
            // Session expired
            $session->setFlashdata('flash_error', 'Your session has expired. Please log in again.');
            return redirect()->to('/admin/login');
        }

        // Check if user has agency assignment
        $agencyId = $auth->agencyId();
        if (empty($agencyId)) {
            // User doesn't have agency assignment - redirect to admin portal
            $session->setFlashdata('flash_error', 'You do not have access to the Agency Portal.');
            return redirect()->to('/admin/dashboard');
        }

        // Verify agency exists and is active
        $agencyModel = new \App\Models\AgencyModel();
        $agency = $agencyModel->find($agencyId);
        
        if (!$agency) {
            // Agency not found
            $auth->logout();
            $session->setFlashdata('flash_error', 'Your assigned agency was not found. Please contact administrator.');
            return redirect()->to('/admin/login');
        }

        if ($agency['status'] !== 'active') {
            // Agency is not active
            $auth->logout();
            $session->setFlashdata('flash_error', 'Your assigned agency is currently inactive. Please contact administrator.');
            return redirect()->to('/admin/login');
        }

        // All checks passed - user can access Agency Portal
        return null;
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No after processing needed for this filter
        return null;
    }
}
