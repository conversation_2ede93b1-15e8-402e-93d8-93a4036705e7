<?php

namespace App\Controllers\Agency;

use App\Models\AgencyModel;

class AgencyProfileController extends AgencyBaseController
{
    protected $agencyModel;

    public function __construct()
    {
        parent::__construct();
        $this->agencyModel = new AgencyModel();
    }

    /**
     * Display agency profile information
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            return redirect()->to('/agency/auth/login');
        }

        $agencyId = $this->getCurrentAgencyId();
        if (!$agencyId) {
            $this->setFlashMessage('error', 'Agency information not found.');
            return redirect()->to('/agency/dashboard');
        }

        $agency = $this->agencyModel->find($agencyId);
        if (!$agency) {
            $this->setFlashMessage('error', 'Agency not found.');
            return redirect()->to('/agency/dashboard');
        }

        $data = [
            'pageTitle' => 'Agency Profile',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => '/agency/dashboard'],
                ['title' => 'Agency Profile', 'url' => '']
            ],
            'agency' => $agency,
            'validation' => \Config\Services::validation()
        ];

        return view('agency/agency_profile_index', $data);
    }

    /**
     * Update agency profile information
     */
    public function update()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            return redirect()->to('/agency/auth/login');
        }

        $agencyId = $this->getCurrentAgencyId();
        if (!$agencyId) {
            $this->setFlashMessage('error', 'Agency information not found.');
            return redirect()->to('/agency/dashboard');
        }

        // Validation rules for agency profile (only editable fields)
        $validation = \Config\Services::validation();
        $validation->setRules([
            'name' => 'required|min_length[3]|max_length[255]',
            'address' => 'permit_empty|max_length[1000]',
            'phone' => 'permit_empty|max_length[20]',
            'email' => 'permit_empty|valid_email|max_length[100]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('validation', $validation);
        }

        try {
            $updateData = [
                'name' => $this->request->getPost('name'),
                'address' => $this->request->getPost('address'),
                'phone' => $this->request->getPost('phone'),
                'email' => $this->request->getPost('email')
            ];

            // Check if email is already taken by another agency
            if (!empty($updateData['email'])) {
                $existingAgency = $this->agencyModel->where('email', $updateData['email'])
                                                   ->where('id !=', $agencyId)
                                                   ->first();
                if ($existingAgency) {
                    return redirect()->back()->withInput()
                                   ->with('flash_error', 'Email address is already in use by another agency.');
                }
            }

            // Update agency record
            if ($this->agencyModel->update($agencyId, $updateData)) {
                $this->setFlashMessage('success', 'Agency profile updated successfully.');
            } else {
                $this->setFlashMessage('error', 'Failed to update agency profile.');
            }

            return redirect()->to('/agency/profile');

        } catch (\Exception $e) {
            log_message('error', 'Agency profile update failed: ' . $e->getMessage());
            return redirect()->back()->withInput()
                           ->with('flash_error', 'Failed to update agency profile. Please try again.');
        }
    }
}
