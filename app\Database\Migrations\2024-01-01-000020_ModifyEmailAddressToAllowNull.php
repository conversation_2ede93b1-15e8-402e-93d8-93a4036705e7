<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class ModifyEmailAddressToAllowNull extends Migration
{
    public function up()
    {
        // Use raw SQL to modify the column to allow NULL values
        // This preserves the existing unique constraint
        $this->db->query('ALTER TABLE employee_personal_information MODIFY COLUMN email_address VARCHAR(100) NULL');
    }

    public function down()
    {
        // Use raw SQL to revert the column to NOT NULL
        $this->db->query('ALTER TABLE employee_personal_information MODIFY COLUMN email_address VARCHAR(100) NOT NULL');
    }
}
