<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<!-- Navigation Buttons -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="<?= base_url('agency/dashboard') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div>
        <a href="<?= base_url('agency/onboarding/create') ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Create New Employee
        </a>
    </div>
</div>

<!-- Page Header -->
<div class="text-center mb-4">
    <h2 class="mb-1">
        <i class="bi bi-person-plus-fill text-primary"></i> Employee Onboarding Management
    </h2>
    <p class="text-muted mb-0">Manage employee onboarding process and track progress</p>
</div>

<!-- Dashboard Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Employees</h6>
                        <h3 class="mb-0"><?= $stats['total_employees'] ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people-fill fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Pending Onboarding</h6>
                        <h3 class="mb-0"><?= $stats['pending_onboarding'] ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-hourglass-split fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">In Progress</h6>
                        <h3 class="mb-0"><?= $stats['in_progress_onboarding'] ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-clockwise fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Active Employees</h6>
                        <h3 class="mb-0"><?= $stats['active_employees'] ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle-fill fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employee Management -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-list-ul text-primary"></i>
            Employee List
        </h5>
    </div>
    <div class="card-body">
        <!-- Simple Search Controls -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" id="searchInput" class="form-control" 
                           placeholder="Search employees...">
                </div>
            </div>
        </div>

        <!-- Employee List -->
        <div class="table-responsive">
            <table id="employeesTable" class="table table-hover" style="width:100%">
                <thead class="table-light">
                    <tr>
                        <th>Employee</th>
                        <th>Email</th>
                        <th>Onboard Status</th>
                        <th>Employment Status</th>
                        <th>Created</th>
                        <th width="350px">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($employees)): ?>
                        <?php foreach ($employees as $employee): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-primary text-white me-2">
                                            <?= strtoupper(substr($employee['first_name'], 0, 1) . substr($employee['last_name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <strong><?= esc($employee['first_name'] . ' ' . $employee['last_name']) ?></strong>
                                            <?php if (!empty($employee['middle_name'])): ?>
                                                <br><small class="text-muted"><?= esc($employee['middle_name']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>

                                <td><?= esc($employee['email_address']) ?></td>
                                <td>
                                    <?php
                                    $statusColors = [
                                        'pending' => 'warning',
                                        'in_progress' => 'info',
                                        'completed' => 'success',
                                        'rejected' => 'danger'
                                    ];
                                    $statusColor = $statusColors[$employee['onboard_status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>">
                                        <?= ucfirst(str_replace('_', ' ', $employee['onboard_status'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $empStatusColors = [
                                        'pending' => 'warning',
                                        'active' => 'success',
                                        'inactive' => 'secondary',
                                        'terminated' => 'danger'
                                    ];
                                    $empStatusColor = $empStatusColors[$employee['employment_status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $empStatusColor ?>">
                                        <?= ucfirst($employee['employment_status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('M d, Y', strtotime($employee['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="d-flex gap-1 justify-content-center flex-wrap">
                                        <?php if ($employee['onboard_status'] === 'submitted'): ?>
                                            <button type="button" class="btn btn-outline-info btn-action"
                                                    onclick="showStatusTracker(<?= $employee['id'] ?>)" title="Status Tracker">
                                                <i class="bi bi-list-check"></i>
                                                <span class="d-none d-sm-inline ms-1">Status Tracker</span>
                                            </button>
                                        <?php else: ?>
                                            <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/profile') ?>"
                                               class="btn btn-outline-primary btn-action" title="View Profile">
                                                <i class="bi bi-eye"></i>
                                                <span class="d-none d-sm-inline ms-1">Profile</span>
                                            </a>
                                            <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/education') ?>"
                                               class="btn btn-outline-info btn-action" title="Education">
                                                <i class="bi bi-mortarboard"></i>
                                                <span class="d-none d-sm-inline ms-1">Education</span>
                                            </a>
                                            <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/memberships') ?>"
                                               class="btn btn-outline-success btn-action" title="Memberships">
                                                <i class="bi bi-award"></i>
                                                <span class="d-none d-sm-inline ms-1">Membership</span>
                                            </a>
                                            <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/files') ?>"
                                               class="btn btn-outline-warning btn-action" title="Files">
                                                <i class="bi bi-file-earmark"></i>
                                                <span class="d-none d-sm-inline ms-1">Files</span>
                                            </a>
                                            <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/employment-history') ?>"
                                               class="btn btn-outline-warning btn-action" title="Employment History">
                                                <i class="bi bi-briefcase"></i>
                                                <span class="d-none d-sm-inline ms-1">History</span>
                                            </a>
                                            <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/assessment') ?>"
                                               class="btn btn-outline-secondary btn-action" title="Assessment Report">
                                                <i class="bi bi-clipboard-check"></i>
                                                <span class="d-none d-sm-inline ms-1">Assessment</span>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}

/* Enhanced action buttons */
.btn-action {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    min-width: 80px;
    transition: all 0.2s ease;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-action i {
    font-size: 1rem;
}

/* Mobile responsive adjustments */
@media (max-width: 767.98px) {
    .btn-action {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
        min-width: 60px;
        margin-bottom: 0.25rem;
    }
    
    .btn-action i {
        font-size: 0.875rem;
    }
    
    .btn-action span {
        font-size: 0.65rem;
    }
    
    /* Stack buttons vertically on very small screens */
    .d-flex.gap-1 {
        flex-direction: column;
        align-items: center;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .btn-action {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
        min-width: 70px;
    }
}

/* Tablet view - show horizontal layout */
@media (min-width: 576px) {
    .d-flex.gap-1 {
        flex-direction: row;
        justify-content: center;
    }
}

/* Simple table styling */
.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>

<!-- Status Tracker Modal -->
<div class="modal fade" id="statusTrackerModal" tabindex="-1" aria-labelledby="statusTrackerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusTrackerModalLabel">
                    <i class="bi bi-list-check"></i> Employee Status Tracker
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="statusTrackerContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading status information...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Simple DataTables initialization
    var table = $('#employeesTable').DataTable({
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        order: [[4, 'desc']], // Order by created date
        columnDefs: [{
            targets: [5], // Actions column
            orderable: false,
            searchable: false
        }],
        language: {
            search: "",
            searchPlaceholder: "Search...",
            emptyTable: `
                <div class="text-center py-5">
                    <i class="bi bi-person-x text-muted" style="font-size: 4rem;"></i>
                    <h5 class="text-muted mt-3">No employees found</h5>
                    <p class="text-muted">Start by creating your first employee record.</p>
                    <a href="<?= base_url('agency/onboarding/create') ?>" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Create New Employee
                    </a>
                </div>
            `
        }
    });

    // Hide default search box and use custom one
    $('.dataTables_filter').hide();
    
    // Custom search input
    $('#searchInput').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Initialize tooltips
    $('[title]').tooltip();
});

// Function to show status tracker modal
function showStatusTracker(employeeId) {
    // Reset modal content
    $('#statusTrackerContent').html(`
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading status information...</p>
        </div>
    `);

    // Show modal
    var modal = new bootstrap.Modal(document.getElementById('statusTrackerModal'));
    modal.show();

    // Fetch status data
    fetch('<?= base_url('agency/onboarding/') ?>' + employeeId + '/status-tracker', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayStatusTracker(data.employee);
        } else {
            $('#statusTrackerContent').html(`
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error loading status information: ${data.message}
                </div>
            `);
        }
    })
    .catch(error => {
        $('#statusTrackerContent').html(`
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                Error loading status information. Please try again.
            </div>
        `);
    });
}

// Function to display status tracker content
function displayStatusTracker(employee) {
    const statusBadges = {
        'pending': 'warning',
        'submitted': 'info',
        'resend': 'warning',
        'rejected': 'danger',
        'approved': 'success'
    };

    const employmentStatusBadges = {
        'pending': 'warning',
        'active': 'success',
        'inactive': 'secondary',
        'terminated': 'danger'
    };

    let content = `
        <div class="row mb-3">
            <div class="col-12">
                <h6 class="fw-bold text-primary">Employee: ${employee.first_name} ${employee.last_name}</h6>
                <small class="text-muted">Employment Number: ${employee.employment_number || 'Not assigned'}</small>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="timeline">
    `;

    // Onboard Status
    content += `
        <div class="timeline-item">
            <div class="timeline-marker bg-${statusBadges[employee.onboard_status] || 'secondary'}"></div>
            <div class="timeline-content">
                <h6 class="mb-1">Onboarding Status</h6>
                <span class="badge bg-${statusBadges[employee.onboard_status] || 'secondary'}">${employee.onboard_status.charAt(0).toUpperCase() + employee.onboard_status.slice(1)}</span>
                ${employee.onboard_status_at ? `<small class="d-block text-muted mt-1">Updated: ${new Date(employee.onboard_status_at).toLocaleString()}</small>` : ''}
                ${employee.onboard_status_remarks ? `<small class="d-block text-muted">Remarks: ${employee.onboard_status_remarks}</small>` : ''}
            </div>
        </div>
    `;

    // HR Review Status
    if (employee.hr_review_status) {
        content += `
            <div class="timeline-item">
                <div class="timeline-marker bg-${statusBadges[employee.hr_review_status] || 'secondary'}"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">HR Review</h6>
                    <span class="badge bg-${statusBadges[employee.hr_review_status] || 'secondary'}">${employee.hr_review_status.charAt(0).toUpperCase() + employee.hr_review_status.slice(1)}</span>
                    ${employee.hr_review_status_at ? `<small class="d-block text-muted mt-1">Updated: ${new Date(employee.hr_review_status_at).toLocaleString()}</small>` : ''}
                </div>
            </div>
        `;
    }

    // Supervisor Review Status
    if (employee.supervisor_review_status) {
        content += `
            <div class="timeline-item">
                <div class="timeline-marker bg-${statusBadges[employee.supervisor_review_status] || 'secondary'}"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">Supervisor Review</h6>
                    <span class="badge bg-${statusBadges[employee.supervisor_review_status] || 'secondary'}">${employee.supervisor_review_status.charAt(0).toUpperCase() + employee.supervisor_review_status.slice(1)}</span>
                    ${employee.supervisor_review_status_at ? `<small class="d-block text-muted mt-1">Updated: ${new Date(employee.supervisor_review_status_at).toLocaleString()}</small>` : ''}
                </div>
            </div>
        `;
    }

    // CEO Review Status
    if (employee.ceo_review_status) {
        content += `
            <div class="timeline-item">
                <div class="timeline-marker bg-${statusBadges[employee.ceo_review_status] || 'secondary'}"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">CEO Review</h6>
                    <span class="badge bg-${statusBadges[employee.ceo_review_status] || 'secondary'}">${employee.ceo_review_status.charAt(0).toUpperCase() + employee.ceo_review_status.slice(1)}</span>
                    ${employee.ceo_review_status_at ? `<small class="d-block text-muted mt-1">Updated: ${new Date(employee.ceo_review_status_at).toLocaleString()}</small>` : ''}
                </div>
            </div>
        `;
    }

    // Employment Status
    if (employee.employment_status) {
        content += `
            <div class="timeline-item">
                <div class="timeline-marker bg-${employmentStatusBadges[employee.employment_status] || 'secondary'}"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">Employment Status</h6>
                    <span class="badge bg-${employmentStatusBadges[employee.employment_status] || 'secondary'}">${employee.employment_status.charAt(0).toUpperCase() + employee.employment_status.slice(1)}</span>
                    ${employee.employment_status_at ? `<small class="d-block text-muted mt-1">Updated: ${new Date(employee.employment_status_at).toLocaleString()}</small>` : ''}
                </div>
            </div>
        `;
    }

    content += `
                </div>
            </div>
        </div>

        <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #dee2e6;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #dee2e6;
        }
        </style>
    `;

    $('#statusTrackerContent').html(content);
}
</script>
<?= $this->endSection() ?>
