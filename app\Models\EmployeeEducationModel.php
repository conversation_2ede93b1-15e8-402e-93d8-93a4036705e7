<?php

namespace App\Models;

use CodeIgniter\Model;

class EmployeeEducationModel extends Model
{
    protected $table = 'employee_education';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'employee_id',
        'qualification_id',
        'qualification_type',
        'qualification_name',
        'course_taken',
        'units',
        'institution',
        'course_duration',
        'completion_year',
        'certificate_number',
        'is_verified',
        'document_path'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'employee_id' => 'required|integer',
        'qualification_id' => 'permit_empty|integer',
        'qualification_type' => 'required|in_list[basic,additional]',
        'qualification_name' => 'required|min_length[2]|max_length[255]',
        'course_taken' => 'permit_empty|max_length[255]',
        'units' => 'permit_empty',
        'institution' => 'permit_empty|max_length[255]',
        'course_duration' => 'permit_empty|max_length[50]',
        'completion_year' => 'permit_empty|integer|greater_than[1900]|less_than_equal_to[2030]',
        'certificate_number' => 'permit_empty|max_length[100]',
        'is_verified' => 'permit_empty|in_list[0,1]',
        'document_path' => 'permit_empty|max_length[500]'
    ];

    protected $validationMessages = [
        'qualification_name' => [
            'required' => 'Qualification name is required.',
            'min_length' => 'Qualification name must be at least 2 characters long.'
        ],
        'completion_year' => [
            'greater_than' => 'Please enter a valid completion year.',
            'less_than_equal_to' => 'Completion year cannot be in the future.'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Get education records by employee ID
     */
    public function getEducationByEmployee(int $employeeId): array
    {
        return $this->where('employee_id', $employeeId)
                   ->orderBy('qualification_type', 'ASC')
                   ->orderBy('completion_year', 'DESC')
                   ->findAll();
    }

    /**
     * Get basic qualification for employee
     */
    public function getBasicQualification(int $employeeId): ?array
    {
        return $this->where(['employee_id' => $employeeId, 'qualification_type' => 'basic'])
                   ->first();
    }

    /**
     * Get additional qualifications for employee
     */
    public function getAdditionalQualifications(int $employeeId): array
    {
        return $this->where(['employee_id' => $employeeId, 'qualification_type' => 'additional'])
                   ->orderBy('completion_year', 'DESC')
                   ->findAll();
    }

    /**
     * Check if employee has basic qualification
     */
    public function hasBasicQualification(int $employeeId): bool
    {
        return $this->where(['employee_id' => $employeeId, 'qualification_type' => 'basic'])
                   ->countAllResults() > 0;
    }

    /**
     * Get education statistics for employee
     */
    public function getEducationStats(int $employeeId): array
    {
        $total = $this->where('employee_id', $employeeId)->countAllResults();
        $verified = $this->where(['employee_id' => $employeeId, 'is_verified' => 1])->countAllResults();
        $withDocuments = $this->where('employee_id', $employeeId)
                             ->where('document_path IS NOT NULL')
                             ->where('document_path !=', '')
                             ->countAllResults();

        // Count basic and additional qualifications
        $basic = $this->where(['employee_id' => $employeeId, 'qualification_type' => 'basic'])->countAllResults();
        $additional = $this->where(['employee_id' => $employeeId, 'qualification_type' => 'additional'])->countAllResults();

        return [
            'total_qualifications' => $total,
            'verified_qualifications' => $verified,
            'qualifications_with_documents' => $withDocuments,
            'verification_percentage' => $total > 0 ? round(($verified / $total) * 100, 1) : 0,
            'basic_qualifications' => $basic,
            'additional_qualifications' => $additional
        ];
    }

    /**
     * Update verification status
     */
    public function updateVerificationStatus(int $educationId, bool $isVerified): bool
    {
        return $this->update($educationId, ['is_verified' => $isVerified ? 1 : 0]);
    }

    /**
     * Delete education record with document cleanup
     */
    public function deleteEducationRecord(int $educationId): bool
    {
        $education = $this->find($educationId);
        
        if ($education && !empty($education['document_path'])) {
            $filePath = ROOTPATH . $education['document_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        
        return $this->delete($educationId);
    }

    /**
     * Get education records with employee details for agency
     */
    public function getEducationWithEmployeeByAgency(int $agencyId): array
    {
        return $this->select('employee_education.*, employee_personal_information.first_name, employee_personal_information.last_name, employee_personal_information.employment_number')
                   ->join('employee_personal_information', 'employee_personal_information.id = employee_education.employee_id')
                   ->where('employee_personal_information.agency_id', $agencyId)
                   ->orderBy('employee_education.created_at', 'DESC')
                   ->findAll();
    }
}
