<?php

namespace App\Controllers\Agency;

use App\Controllers\Agency\AgencyBaseController;

class AgencyDashboardController extends AgencyBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Agency Dashboard - Employee Management Removed
     */
    public function index()
    {
        // Get agency ID from authenticated user
        $agencyId = $this->getCurrentAgencyId();

        if (!$agencyId) {
            $this->setFlashMessage('error', 'No agency assignment found. Please contact administrator.');
            return redirect()->to('/admin/dashboard');
        }

        $this->setPageTitle('Agency Dashboard');
        $this->addBreadcrumb('Dashboard');

        // Simple dashboard data without employee dependencies
        $data = [
            'agencyId' => $agencyId,
            'agency' => $this->getCurrentAgency(),
            'systemStatus' => 'online',
            'featuresCount' => 6,
            'alertsCount' => 0
        ];

        return $this->renderView('agency/dashboard/agency_dashboard_index', $data);
    }

    /**
     * Get dashboard statistics as JSON (for AJAX updates) - Employee Management Removed
     */
    public function getStats()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            return $this->response->setJSON(['error' => 'Unauthorized'])->setStatusCode(401);
        }

        // Get agency information for future use
        $agencyId = $this->getCurrentAgencyId();
        $agency = $this->getCurrentAgency();

        $stats = [
            'agency_id' => $agencyId,
            'agency_name' => $agency['name'] ?? 'Unknown Agency',
            'system_status' => 'online',
            'features_count' => 6,
            'alerts_count' => 0,
            'last_updated' => date('Y-m-d H:i:s')
        ];

        return $this->response->setJSON($stats);
    }

    /**
     * Get recent activity feed - Employee Management Removed
     */
    public function getRecentActivity()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            return $this->response->setJSON(['error' => 'Unauthorized'])->setStatusCode(401);
        }

        // Get agency information for future use
        $agencyId = $this->getCurrentAgencyId();
        $agency = $this->getCurrentAgency();

        $activities = [
            [
                'type' => 'system_status',
                'message' => 'Agency Portal is running smoothly for ' . ($agency['name'] ?? 'Agency'),
                'timestamp' => date('Y-m-d H:i:s'),
                'icon' => 'bi-check-circle',
                'color' => 'success'
            ],
            [
                'type' => 'portal_access',
                'message' => 'Dashboard accessed successfully',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-5 minutes')),
                'icon' => 'bi-shield-check',
                'color' => 'info'
            ]
        ];

        return $this->response->setJSON($activities);
    }
}
